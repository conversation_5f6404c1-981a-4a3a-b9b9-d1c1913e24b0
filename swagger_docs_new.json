{"swagger": "2.0", "info": {"description": "footstone-open-api online doc", "version": "1.0", "title": "footstone-open-api online doc", "termsOfService": "https://www.tsign.cn/", "contact": {"email": "<EMAIL>"}}, "host": "footstone-open-api.testk8s.tsign.cn", "basePath": "/", "tags": [{"name": "专属云本地相关接口", "description": "Dedicated Cloud Rest"}, {"name": "文档相关对外接口", "description": "Document Rest"}, {"name": "流程模板", "description": "流程模板管理模块内部API"}, {"name": "签署流程对外接口", "description": "Sign Flow Rest"}, {"name": "证书相关内部接口", "description": "Cert Internal Rest"}, {"name": "证书相关对外接口", "description": "Cert Rest"}], "paths": {"/v1/certs/apply-cert": {"post": {"tags": ["证书相关对外接口"], "summary": "制证接口", "operationId": "applyCertUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/CertApplyCertRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«制证返回值»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/certs/create-cert-task": {"post": {"tags": ["证书相关对外接口"], "summary": "申请证书制证任务", "operationId": "createCertTaskUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/CertCreateTaskRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«创建任务»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/certs/delay-cert": {"post": {"tags": ["证书相关对外接口"], "summary": "延期接口", "operationId": "delayCertUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/CertDelayCertRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«延期证书返回值»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/certs/noaccount/create-cert-task": {"post": {"tags": ["证书相关对外接口"], "summary": "无账号申请证书制证任务", "operationId": "noAccountCreateCertTaskUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/CertNoAccountCreateTaskRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«创建任务»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/certs/revoke-cert": {"post": {"tags": ["证书相关对外接口"], "summary": "吊销接口", "operationId": "revokeCertUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/CertRevokeCertRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/certs/task/{taskId}": {"get": {"tags": ["证书相关对外接口"], "summary": "查询证书任务", "operationId": "queryCertByTaskUsingGET", "produces": ["*/*"], "parameters": [{"name": "taskId", "in": "path", "description": "任务id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«根据任务id查询返回值»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/certs/update-cert": {"post": {"tags": ["证书相关对外接口"], "summary": "更新接口", "operationId": "updateCertUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/CertUpdateCertRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«更新证书返回值»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/inner-api/certs/init-task": {"post": {"tags": ["证书相关内部接口"], "summary": "初始化证书任务", "operationId": "initTaskUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/InitTaskRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«初始化任务返回值»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/inner-api/certs/query-task": {"post": {"tags": ["证书相关内部接口"], "summary": "查询证书任务", "operationId": "queryTaskUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/QueryTaskRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«查询任务返回值»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v1/inner-api/certs/task/auth-credentials": {"post": {"tags": ["证书相关内部接口"], "summary": "查询证书任务登录凭证", "operationId": "queryCertTaskAuthCredentialsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/QueryCertTaskAuthCredentialsRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«查询登录凭证返回值»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/dedicated-cloud/platform-hash-sign": {"post": {"tags": ["专属云本地相关接口"], "summary": "专属云平台证书摘要签署", "operationId": "platformHashSignUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/DedicatedCloudPlatformHashSignRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«DedicatedCloudPlatformHashSignResponse»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/dedicated-cloud/verify": {"post": {"tags": ["专属云本地相关接口"], "summary": "专属云本地验证", "operationId": "verifyUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/DedicatedCloudVerifyRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«DedicatedCloudVerifyResponse»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/dedicated-cloud/{signFlowId}/doc-sign/confirm": {"post": {"tags": ["专属云本地相关接口"], "summary": "专属云同步签署结果", "operationId": "confirmSignResultUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/DedicatedCloudConfirmSignResultReq"}}, {"name": "signFlowId", "in": "path", "description": "签署流程id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/dedicated-cloud/{signFlowId}/file-hash-sign": {"post": {"tags": ["专属云本地相关接口"], "summary": "专属云摘要签署", "operationId": "docHashSignUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/DedicatedCloudHashSignRequest"}}, {"name": "signFlowId", "in": "path", "description": "签署流程id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«DedicatedCloudHashSignResponse»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/files/merge-files": {"post": {"tags": ["文档相关对外接口"], "summary": "文件合并", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=186839296", "operationId": "mergeFilesUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/MergeFilesRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«合并文件返回参数»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/files/{fileId}/keyword-positions": {"post": {"tags": ["文档相关对外接口"], "summary": "搜索关键字", "operationId": "searchKeywordUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "fileId", "in": "path", "description": "文件id", "required": true, "type": "string"}, {"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/SearchKeywordRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«搜索关键字坐标返回参数»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/inner-api/flow-template/checkNames": {"post": {"tags": ["流程模板"], "summary": "流程模板保存时二要素校验", "operationId": "checkNamesUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/发起签署前-批量参与人二要素校验请求参数"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«发起签署前-批量参与人二要素校验响应参数»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/inner-api/flow-template/config-info": {"get": {"tags": ["流程模板"], "summary": "根据openId获取流程模板创建/编辑时的配置参数", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E5%BC%80%E6%94%BE-%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E8%AF%A6%E8%AE%BE-%E6%8E%A5%E5%8F%A3%E5%AE%9A%E4%B9%89-5.1%E3%80%81%E6%A0%B9%E6%8D%AEopenId%E8%8E%B7%E5%8F%96%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E5%88%9B%E5%BB%BA/%E7%BC%96%E8%BE%91%E6%97%B6%E7%9A%84%E9%85%8D%E7%BD%AE%E5%8F%82%E6%95%B0", "operationId": "getFlowTemplateConfigInfoUsingGET", "produces": ["*/*"], "parameters": [{"name": "checkOpenId", "in": "query", "description": "是否校验openId有效性", "required": false, "type": "boolean", "allowEmptyValue": false}, {"name": "openId", "in": "query", "description": "openId", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "queryFlowTemplateAccountInfo", "in": "query", "description": "是否校验openId有效性", "required": false, "type": "boolean", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«FlowTemplateConfigInfoResp»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/inner-api/flow-template/create-or-edit": {"post": {"tags": ["流程模板"], "summary": "创建/编辑流程模板", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E5%BC%80%E6%94%BE-%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E8%AF%A6%E8%AE%BE-%E6%8E%A5%E5%8F%A3%E5%AE%9A%E4%B9%89-5.2%E3%80%81%E5%88%9B%E5%BB%BA/%E7%BC%96%E8%BE%91%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF", "operationId": "createOrEditFlowTemplateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "req", "description": "req", "required": true, "schema": {"$ref": "#/definitions/流程发起业务模型"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«FlowTemplateResp»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/inner-api/flow-template/participants-check": {"post": {"tags": ["流程模板"], "summary": "模板签署设置校验", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E5%BC%80%E6%94%BE-%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E8%AF%A6%E8%AE%BE-%E6%8E%A5%E5%8F%A3%E5%AE%9A%E4%B9%89-5.2%E3%80%81%E5%88%9B%E5%BB%BA/%E7%BC%96%E8%BE%91%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF", "operationId": "participantsCheckUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "req", "description": "req", "required": true, "schema": {"$ref": "#/definitions/模板签署设置校验请求参数"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/inner-api/flow-template/sign-url": {"post": {"tags": ["流程模板"], "summary": "填写完成,前端轮询获取签署链接", "operationId": "getSignUrlUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "clientType", "in": "query", "description": "客户端,PC或者H5", "required": false, "type": "string"}, {"name": "cooperationId", "in": "query", "description": "cooperationId", "required": true, "type": "string"}, {"name": "firstRequest", "in": "query", "description": "true为填写完成第一次请求,false为再次进入填写页面请求", "required": false, "type": "boolean"}, {"name": "redirectDelayTime", "in": "query", "description": "redirectDelayTime", "required": false, "type": "integer", "format": "int32"}, {"name": "redirectUrl", "in": "query", "description": "redirectUrl", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«发起签署前-批量参与人二要素校验响应参数»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/inner-api/flow-template/sign-url-permission": {"post": {"tags": ["流程模板"], "summary": "是否能获取签署链接权限", "operationId": "getSignUrlPermissionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "signUrlReq", "description": "signUrlReq", "required": true, "schema": {"$ref": "#/definitions/SignUrlReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/inner-api/flow-template/{flowTemplateId}/detail": {"get": {"tags": ["流程模板"], "summary": "获取流程模板详情", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#id-%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E5%BC%80%E6%94%BE-%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E8%AF%A6%E8%AE%BE-%E6%8E%A5%E5%8F%A3%E5%AE%9A%E4%B9%89-5.5%E3%80%81%E8%8E%B7%E5%8F%96%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E8%AF%A6%E6%83%85", "operationId": "flowTemplateDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "flowTemplateId", "in": "path", "description": "flowTemplateId", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«流程发起业务模型»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-flow/create-by-sign-template": {"post": {"tags": ["签署流程对外接口"], "summary": "通过流程模板创建合同拟定和签署流程", "operationId": "createFlowByTemplateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/通过流程模板创建合同拟定和签署流程请求参数"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«通过流程模板创建合同拟定和签署流程返回参数»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-flow/list": {"post": {"tags": ["签署流程对外接口"], "summary": "查询合同拟定流程列表", "operationId": "getSignFlowListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/SignFlowListRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«流程列表返回参数»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-flow/{processId}/bizContext": {"post": {"tags": ["签署流程对外接口"], "summary": "获取当次填写流程业务上下文", "operationId": "getFillBizContextUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "processId", "in": "path", "description": "流程id", "required": true, "type": "string"}, {"name": "tpiId", "in": "query", "description": "tpiId", "required": true, "type": "string", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«撤销合同拟定返回参数»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-flow/{signFlowId}/draft-detail": {"get": {"tags": ["签署流程对外接口"], "summary": "查询合同拟定流程详情", "operationId": "getDraftDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "internalUrl", "in": "query", "description": "是否内网地址", "required": false, "type": "boolean", "allowEmptyValue": false}, {"name": "signFlowId", "in": "path", "description": "签署流程id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«合同拟定详情返回参数»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-flow/{signFlowId}/draft-url": {"post": {"tags": ["签署流程对外接口"], "summary": "获取填写页面链接", "operationId": "getDraftUrlUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/DraftUrlRequest"}}, {"name": "signFlowId", "in": "path", "description": "签署流程id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«填写链接返回结果»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-flow/{signFlowId}/rescind": {"post": {"tags": ["签署流程对外接口"], "summary": "撤销合同拟定", "operationId": "rescindSignFlowUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "signFlowId", "in": "path", "description": "流程id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«撤销合同拟定返回参数»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-flow/{signFlowId}/status": {"get": {"tags": ["签署流程对外接口"], "summary": "查询合同拟定和签署流程状态", "operationId": "getSignFlowStatusUsingGET", "produces": ["*/*"], "parameters": [{"name": "signFlowId", "in": "path", "description": "签署流程id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«合同拟定以及签署流程状态返回结果»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-flow/{signFlowId}/urge-filling": {"post": {"tags": ["签署流程对外接口"], "summary": "催填", "operationId": "urgeFillingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "param", "description": "param", "required": true, "schema": {"$ref": "#/definitions/UrgeFillingRequest"}}, {"name": "signFlowId", "in": "path", "description": "流程id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates": {"get": {"tags": ["流程模板"], "summary": "查询流程模板列表", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=163321361", "operationId": "flowTemplateListUsingGET", "produces": ["*/*"], "parameters": [{"in": "body", "name": "orgId", "description": "机构用户id", "required": true, "schema": {"type": "string"}}, {"in": "body", "name": "pageNum", "description": "分页页码，如果不传默从第一页返回。", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"in": "body", "name": "pageSize", "description": "每个分页包含的数据条数，如果不传默认为20，单页最大20。", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"in": "body", "name": "status", "description": "0-关闭\n1-开启\n默认全部", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«FlowTemplateListResp»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates/copy": {"post": {"tags": ["流程模板"], "summary": "复制流程模板", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=180981132", "operationId": "flowTemplateCopyUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "req", "description": "req", "required": true, "schema": {"$ref": "#/definitions/FlowTemplateCopyReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«FlowTemplateCopyResp»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates/delete": {"post": {"tags": ["流程模板"], "summary": "删除流程模板", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=163321355", "operationId": "flowTemplateDeleteUrlUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "req", "description": "req", "required": true, "schema": {"$ref": "#/definitions/FlowTemplateDeleteReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates/detail": {"get": {"tags": ["流程模板"], "summary": "查询流程模板详情", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=163321366", "operationId": "flowTemplateDetailUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "internalUrl", "in": "query", "description": "是否内网地址", "required": false, "type": "boolean", "allowEmptyValue": false}, {"name": "orgId", "in": "query", "description": "机构用户id\t校验有无授权manage_org_resource权限", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "queryComponents", "in": "query", "description": "是否需要查询控件信息，默认false\ntrue-是\nfalse-否", "required": false, "type": "boolean", "allowEmptyValue": false}, {"name": "signTemplateId", "in": "query", "description": "流程模板id", "required": true, "type": "string", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«OpenFlowTemplateDetailResp»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates/disable": {"post": {"tags": ["流程模板"], "summary": "关闭流程模板", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=163347177", "operationId": "flowTemplateDisableUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "req", "description": "req", "required": true, "schema": {"$ref": "#/definitions/FlowTemplateDisableReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates/enable": {"post": {"tags": ["流程模板"], "summary": "开启流程模板", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=163347175", "operationId": "flowTemplateEnableUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "req", "description": "req", "required": true, "schema": {"$ref": "#/definitions/FlowTemplateEnableReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates/permission": {"get": {"tags": ["流程模板"], "summary": "查询用户在某企业下能否编辑或使用某流程模板", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=163321366", "operationId": "flowTemplatePermissionCheckUsingGET", "produces": ["*/*"], "parameters": [{"name": "orgId", "in": "query", "description": "企业id", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "permission", "in": "query", "description": "1-编辑\n2-使用", "required": false, "type": "integer", "format": "int32", "allowEmptyValue": false}, {"name": "psnId", "in": "query", "description": "用户id", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "signTemplateId", "in": "query", "description": "流程模板id", "required": true, "type": "string", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«FlowTemplatePermissionCheckResp»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates/sign-template-create-url": {"post": {"tags": ["流程模板"], "summary": "获取《创建流程模板》页面链接", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=163319645", "operationId": "flowTemplateCreateUrlUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "req", "description": "req", "required": true, "schema": {"$ref": "#/definitions/FlowTemplateCreateUrlReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«FlowTemplateCreateUrlResp»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/v3/sign-templates/{signTemplateId}/sign-template-edit-url": {"post": {"tags": ["流程模板"], "summary": "获取《编辑流程模板》页面链接", "description": "http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=163321336", "operationId": "flowTemplateEditUrlUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "req", "description": "req", "required": true, "schema": {"$ref": "#/definitions/FlowTemplateEditUrlReq"}}, {"name": "signTemplateId", "in": "path", "description": "流程模板id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/通用返回结果«FlowTemplateEditUrlResp»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "definitions": {"ApplyCertVo": {"type": "object", "properties": {"algorithm": {"type": "string", "description": "算法，RSA,SM2"}, "caRoute": {"type": "string", "description": "申领成功的证书通道名称"}, "certId": {"type": "string", "description": "证书id"}, "encCert": {"type": "string", "description": "加密证书"}, "endDate": {"type": "integer", "format": "int64", "description": "证书结束时间"}, "ext": {"type": "string", "description": "扩展信息"}, "signCert": {"type": "string", "description": "签名证书base64"}, "signSn": {"type": "string", "description": "签名证书SN"}, "startDate": {"type": "integer", "format": "int64", "description": "证书开始时间"}}, "title": "ApplyCertVo"}, "Attachment": {"type": "object", "required": ["downloadUrl"], "properties": {"downloadUrl": {"type": "string", "description": "文件下载地址（30天有效）"}, "fileId": {"type": "string", "description": "附件文件ID"}, "fileName": {"type": "string", "description": "附件文件名称"}}, "title": "Attachment"}, "AuthConfigVo": {"type": "object", "properties": {"orgAvailableAuthModes": {"type": "array", "description": "页面中机构实名认证可选项 【组织机构对公账户打款认证】 ORG_BANK_TRANSFER 【企业支付宝认证】 ORG_ALIPAY_CREDIT 【组织机构授权委托书认证】 ORG_LEGALREP_AUTHORIZATION 【法定代表人本人认证】 ORG_LEGALREP", "items": {"type": "string"}}, "psnAvailableAuthModes": {"type": "array", "description": "页面中个人实名认证可选项 PSN_BANKCARD4 - 个人银行卡四要素认证 PSN_MOBILE3 - 个人运营商三要素认证 PSN_FACE - 刷脸认证", "items": {"type": "string"}}}, "title": "AuthConfigVo"}, "BackInfo": {"type": "object", "title": "BackInfo"}, "CertApplyCertRequest": {"type": "object", "required": ["csr", "taskId"], "properties": {"csr": {"type": "string", "description": "csr"}, "extDataMap": {"type": "object", "description": "拓展参数", "additionalProperties": {"type": "string"}}, "taskId": {"type": "string", "description": "任务id"}}, "title": "CertApplyCertRequest"}, "CertApplyCommonModel": {"type": "object", "properties": {"address": {"type": "string", "description": "地址"}, "agentIdNo": {"type": "string", "description": "代理人身份证号"}, "agentLicenseType": {"type": "integer", "format": "int32", "description": "代理人证件类型"}, "agentName": {"type": "string", "description": "代理人姓名"}, "agentType": {"type": "integer", "format": "int32", "description": "代理人类型(1-2)"}, "agentUserPhone": {"type": "string", "description": "经办人手机号"}, "city": {"type": "string", "description": "市"}, "country": {"type": "string", "description": "国家"}, "department": {"type": "string", "description": "部门"}, "email": {"type": "string", "description": "邮箱"}, "legalUserIdCard": {"type": "string", "description": "法人身份证号"}, "legalUserName": {"type": "string", "description": "法人姓名"}, "mobile": {"type": "string", "description": "手机号"}, "organization": {"type": "string", "description": "组织"}, "phone": {"type": "string", "description": "电话"}, "province": {"type": "string", "description": "省"}}, "title": "CertApplyCommonModel"}, "CertApplyConfigModel": {"type": "object", "properties": {"algorithm": {"type": "string", "description": "算法，默认RSA（枚举值：RSA, SM2）"}, "certId": {"type": "string", "description": "证书id（条件必填，申请类型是续期和更新的必填）"}, "certNum": {"type": "integer", "format": "int32", "description": "制定个数，默认1"}, "certTime": {"type": "string", "description": "证书有效期，默认 ONEYEAR"}, "certType": {"type": "string", "description": "单双证 默认SINGLE（枚举值：SINGLE, DOUBLE）"}, "csr": {"type": "string", "description": "证书请求p10"}, "issuer": {"type": "string", "description": "指定CA通道"}}, "title": "CertApplyConfigModel"}, "CertApplyUserModel": {"type": "object", "required": ["userType"], "properties": {"certName": {"type": "string", "description": "证书名称"}, "licenseNumber": {"type": "string", "description": "证件号码"}, "licenseType": {"type": "integer", "format": "int32", "description": "证件类型"}, "oid": {"type": "string", "description": "制证的用户oid"}, "ou": {"type": "string", "description": "OU"}, "userType": {"type": "integer", "format": "int32", "description": "用户类型 1-个人 2-企业"}}, "title": "CertApplyUserModel"}, "CertAuthMaterial": {"type": "object", "required": ["authMaterialContent", "authMaterialContentFormat", "authMaterialContentType", "authMaterialType"], "properties": {"authMaterialContent": {"type": "string", "description": "授权资料内容"}, "authMaterialContentFormat": {"type": "string", "description": "授权资料内容格式 (jpg,jpeg,png,pdf)"}, "authMaterialContentType": {"type": "string", "description": "授权资料内容类型 (fileKey=文件系统key)"}, "authMaterialType": {"type": "string", "description": "授权资料类型 (authLetter=授权书, idCardCopy=身份证复印件)"}}, "title": "CertAuthMaterial"}, "CertCreateTaskRequest": {"type": "object", "required": ["applyUserModel", "bizType", "operatorType"], "properties": {"applyCommonModel": {"description": "通用信息相关", "$ref": "#/definitions/CertApplyCommonModel"}, "applyConfigModel": {"description": "制证配置项相关", "$ref": "#/definitions/CertApplyConfigModel"}, "applyUserModel": {"description": "用户信息相关", "$ref": "#/definitions/CertApplyUserModel"}, "bizType": {"type": "string", "description": "业务方类型 (枚举: TIANYIN,TIANYIN_OFFLINE,ESHIELD,TCLOUD,PUBCLOUD)"}, "notifyUrl": {"type": "string", "description": "通知地址"}, "operatorType": {"type": "string", "description": "申请类型-（APPLY, DELAY，UPDATE）"}, "redirectUrl": {"type": "string", "description": "重定向地址"}}, "title": "CertCreateTaskRequest"}, "CertDelayCertRequest": {"type": "object", "required": ["certId", "taskId"], "properties": {"certId": {"type": "string", "description": "证书id"}, "csr": {"type": "string", "description": "csr"}, "taskId": {"type": "string", "description": "任务id"}}, "title": "CertDelayCertRequest"}, "CertNoAccountCreateTaskRequest": {"type": "object", "required": ["applyUserModel", "authMaterialList", "bizType", "operatorType"], "properties": {"applyCommonModel": {"description": "通用信息相关", "$ref": "#/definitions/CertApplyCommonModel"}, "applyConfigModel": {"description": "制证配置项相关", "$ref": "#/definitions/CertApplyConfigModel"}, "applyUserModel": {"description": "用户信息相关", "$ref": "#/definitions/CertApplyUserModel"}, "auditedMode": {"type": "string", "description": "审核模式 system或者artificial"}, "authMaterialList": {"type": "array", "description": "授权资料内容列表", "items": {"$ref": "#/definitions/CertAuthMaterial"}}, "bizType": {"type": "string", "description": "业务方类型 (枚举: TIANYIN,TIANYIN_OFFLINE,ESHIELD,TCLOUD,PUBCLOUD)"}, "notifyUrl": {"type": "string", "description": "通知地址"}, "operatorType": {"type": "string", "description": "申请类型-（APPLY, DELAY，UPDATE）"}, "redirectUrl": {"type": "string", "description": "重定向地址"}}, "title": "CertNoAccountCreateTaskRequest"}, "CertPsnMismatchVo": {"type": "object", "properties": {"accountUnbindUrl": {"type": "string", "description": "换绑地址"}, "currentCertName": {"type": "string", "description": "当前登录人名字"}, "currentLicenseNumber": {"type": "string", "description": "当前登录人证件号"}, "currentMobile": {"type": "string", "description": "当前登录人手机号"}, "taskCertName": {"type": "string", "description": "任务中的名字"}, "taskLicenseNumber": {"type": "string", "description": "任务中的证件号"}}, "title": "CertPsnMismatchVo"}, "CertRevokeCertRequest": {"type": "object", "required": ["certId"], "properties": {"certId": {"type": "string", "description": "证书id"}}, "title": "CertRevokeCertRequest"}, "CertUpdateCertRequest": {"type": "object", "required": ["certId", "taskId"], "properties": {"certId": {"type": "string", "description": "证书id"}, "csr": {"type": "string", "description": "csr"}, "taskId": {"type": "string", "description": "任务id"}}, "title": "CertUpdateCertRequest"}, "ChargeConfigVo": {"type": "object", "properties": {"chargeMode": {"type": "integer", "format": "int32", "description": "计费模式 默认0-平台方付费，1-发起方付费，2-签署方付费"}, "orderType": {"type": "string", "description": "订单类型 DISTRIBUTION-渠道套餐"}}, "title": "ChargeConfigVo"}, "CheckNamesRequestBean": {"type": "object", "properties": {"accounts": {"type": "array", "description": "账号列表", "items": {"$ref": "#/definitions/发起签署前-单个参与人二要素校验请求参数"}}, "label": {"type": "string", "description": "签署方"}}, "title": "CheckNamesRequestBean"}, "CheckNamesResponseBean": {"type": "object", "properties": {"accounts": {"type": "array", "description": "账号列表", "items": {"$ref": "#/definitions/发起签署前-单个参与人二要素校验响应参数"}}, "label": {"type": "string", "description": "签署方:甲方、乙方、...、抄送人"}}, "title": "CheckNamesResponseBean"}, "ComponentDetail": {"type": "object", "properties": {"componentDefaultValue": {"type": "string", "description": "控件默认值"}, "componentId": {"type": "string", "description": "控件id"}, "componentKey": {"type": "string", "description": "控件key（在设置文件模板时由用户填写的控件标识）"}, "componentName": {"type": "string", "description": "控件名称"}, "componentPosition": {"description": "控件位置\t", "$ref": "#/definitions/ComponentPosition"}, "componentSize": {"description": "控件尺寸\t", "$ref": "#/definitions/ComponentSize"}, "componentSpecialAttribute": {"description": "控件特有属性\t", "$ref": "#/definitions/ComponentSpecialAttribute"}, "componentTextFormat": {"description": "控件字符样式\t", "$ref": "#/definitions/ComponentTextFormat"}, "componentType": {"type": "integer", "format": "int32", "description": "填写控件类型，\n\n1-单行文本，2-数字，3-日期，6-签约区，\n\n8-多行文本，11-图片"}, "fileId": {"type": "string", "description": "文件ID"}, "normalSignField": {"description": "签署区属性\t", "$ref": "#/definitions/NormalSignField"}, "originCustomComponentId": {"type": "string", "description": "来源自定义控件id"}, "remarkSignField": {"description": "备注签字区属性\t", "$ref": "#/definitions/RemarkSignField"}, "required": {"type": "boolean", "description": "控件是否必填 true-是否，false-否"}}, "title": "ComponentDetail"}, "ComponentPosition": {"type": "object", "properties": {"componentPageNum": {"type": "integer", "format": "int32", "description": "控件所在页码"}, "componentPositionX": {"type": "number", "format": "float", "description": "控件位置横坐标"}, "componentPositionY": {"type": "number", "format": "float", "description": "控件位置纵坐标"}}, "title": "ComponentPosition"}, "ComponentSize": {"type": "object", "properties": {"componentHeight": {"type": "integer", "format": "int32", "description": "控件高度（矩形的上下边距距离，单位为px）\t"}, "componentWidth": {"type": "integer", "format": "int32", "description": "控件宽度（矩形的左右边距距离，单位为px）\t"}}, "title": "ComponentSize"}, "ComponentSpecialAttribute": {"type": "object", "properties": {"componentMaxLength": {"type": "string", "description": "控件可填充长度上限（多少个英文字符，1中文字符=2英文字符）"}, "dateFormat": {"type": "string", "description": "日期格式（日期控件）\t"}, "imageType": {"type": "string", "description": "图片类型（图片控件）\n\nIDCard_widthwise 身份证 横向 锁比例\n\nIDCard_longitudinal 身份证 纵向 锁比例\n\nother 其他 不锁比例"}, "numberFormat": {"type": "string", "description": "数字格式（数字控件）"}, "options": {"type": "array", "description": "选项列表", "items": {"$ref": "#/definitions/Option"}}, "tableContent": {"type": "array", "description": "表格内容(表格控件)", "items": {"$ref": "#/definitions/TableRow"}}}, "title": "ComponentSpecialAttribute"}, "ComponentTextFormat": {"type": "object", "properties": {"bold": {"type": "boolean", "description": "是否加粗"}, "font": {"type": "integer", "format": "int32", "description": "字体"}, "fontSize": {"type": "number", "format": "float", "description": "字体大小, 默认12"}, "horizontalAlignment": {"type": "string", "description": "水平对齐方式 LEFT 靠左,CENTER 水平居中,RIGHT 靠右 TOP顶部,MIDDLE 垂直居中,BOTTOM 底"}, "italic": {"type": "boolean", "description": "是否斜体"}, "textColor": {"type": "string", "description": "字体颜色,默认黑色, #000000"}, "textLineSpacing": {"type": "number", "format": "float", "description": "行间距比例"}, "verticalAlignment": {"type": "string", "description": "垂直对齐方式 LEFT 靠左,CENTER 水平居中,RIGHT 靠右 TOP顶部,MIDDLE 垂直居中,BOTTOM 底"}}, "title": "ComponentTextFormat"}, "Coordinate": {"type": "object", "properties": {"positionX": {"type": "number", "format": "float", "description": "X坐标"}, "positionY": {"type": "number", "format": "float", "description": "Y坐标"}}, "title": "Coordinate"}, "Copier": {"type": "object", "properties": {"copierOrgInfo": {"description": "机构抄送方信息", "$ref": "#/definitions/Org"}, "copierPsnInfo": {"description": "个人抄送方信息 支持场景：①抄送给个人，②抄送给企业的接收人", "$ref": "#/definitions/Psn"}}, "title": "<PERSON><PERSON><PERSON>"}, "CopierOrgInfo": {"type": "object", "properties": {"orgId": {"type": "string", "description": "机构抄送方id"}, "orgName": {"type": "string", "description": "机构抄送方名称"}}, "title": "CopierOrgInfo"}, "CopierPsnInfo": {"type": "object", "properties": {"psnAccount": {"type": "string", "description": "个人抄送方账号，手机号或邮箱"}, "psnId": {"type": "string", "description": "个人抄送方id"}}, "title": "CopierPsnInfo"}, "DedicatedCloudConfirmSignResultReq": {"type": "object", "required": ["description", "signSerialId", "signSuccess", "signTaskId", "tag"], "properties": {"description": {"type": "string", "description": "签署描述信息 执行成功 or 签署失败原因"}, "docHash": {"type": "string", "description": "签署完成后文件hash"}, "fileKey": {"type": "string", "description": "签署后文档fileKey"}, "signResults": {"type": "array", "description": "签署结果列表", "items": {"$ref": "#/definitions/SignTaskResultInfoBean"}}, "signSerialId": {"type": "string", "description": "签署批次号"}, "signSuccess": {"type": "boolean", "description": "是否签署成功"}, "signTaskId": {"type": "string", "description": "签署任务id"}, "tag": {"type": "string", "description": "tag"}}, "title": "DedicatedCloudConfirmSignResultReq"}, "DedicatedCloudHashSignRequest": {"type": "object", "required": ["docHash", "signHash", "signSerialId", "signTaskId", "tag"], "properties": {"certId": {"type": "string", "description": "证书id，优先级：入参证书id > 账号证书id"}, "docHash": {"type": "string", "description": "签署前的原文摘要不能为空"}, "nodeFieldIds": {"type": "array", "description": "签署区id列表", "items": {"type": "string"}}, "signHash": {"type": "string", "description": "待签署的原文摘要不能为空"}, "signSerialId": {"type": "string", "description": "签署批次号"}, "signTaskId": {"type": "string", "description": "签署任务id"}, "signerId": {"type": "string", "description": "签署主体id"}, "tag": {"type": "string", "description": "tag"}}, "title": "DedicatedCloudHashSignRequest"}, "DedicatedCloudHashSignResponse": {"type": "object", "properties": {"certId": {"type": "string", "description": "certId"}, "signLogId": {"type": "string", "description": "signLogId"}, "signResult": {"type": "string", "description": "签名结果"}}, "title": "DedicatedCloudHashSignResponse"}, "DedicatedCloudPlatformHashSignRequest": {"type": "object", "required": ["hash"], "properties": {"hash": {"type": "string", "description": "待签署的原文摘要不能为空"}, "ticket": {"type": "string", "description": "专属云平台摘要签访问令牌"}}, "title": "DedicatedCloudPlatformHashSignRequest"}, "DedicatedCloudPlatformHashSignResponse": {"type": "object", "properties": {"signResult": {"type": "string", "description": "签名结果"}}, "title": "DedicatedCloudPlatformHashSignResponse"}, "DedicatedCloudVerifyRequest": {"type": "object", "required": ["dedicatedCloudId"], "properties": {"dedicatedCloudId": {"type": "string", "description": "专属云项目id"}}, "title": "DedicatedCloudVerifyRequest"}, "DedicatedCloudVerifyResponse": {"type": "object", "properties": {"pass": {"type": "boolean", "description": "验证结果"}}, "title": "DedicatedCloudVerifyResponse"}, "Doc": {"type": "object", "required": ["fileDownloadUrl", "fileId", "fileName"], "properties": {"fileDownloadUrl": {"type": "string", "description": "文件底稿PDF文件的下载链接，有效期60分钟。"}, "fileId": {"type": "string", "description": "文件ID"}, "fileName": {"type": "string", "description": "文件名称"}}, "title": "Doc"}, "DraftInfoVo": {"type": "object", "properties": {"draftFinishTime": {"type": "integer", "format": "int64", "description": " 填写流程结束时间"}, "draftStartTime": {"type": "integer", "format": "int64", "description": " 填写流程开始时间"}, "draftStatus": {"type": "integer", "format": "int32", "description": " 填写流程状态"}, "drafters": {"type": "array", "description": "填写方信息列表", "items": {"$ref": "#/definitions/DrafterVo"}}, "signFlowId": {"type": "string", "description": "签署流程ID"}, "signFlowInitiator": {"description": "拟定流程发起方", "$ref": "#/definitions/SignFlowInitiatorVo"}, "signFlowTitle": {"type": "string", "description": " 签署流程标题"}}, "title": "DraftInfoVo"}, "DraftUrlRequest": {"type": "object", "properties": {"clientType": {"type": "string", "description": "客户端类型: ALL - 自动适配移动端或PC端;H5 - 移动端适配;PC - PC端适配;默认ALL"}, "needLogin": {"type": "boolean", "description": "是否需要登录"}, "operator": {"description": "填写操作人（个人填写方本人为操作人，机构填写方经办人为操作人）", "$ref": "#/definitions/个人填写方信息"}, "redirectConfig": {"description": "重定向配置项", "$ref": "#/definitions/重定向配置项"}, "urlType": {"type": "integer", "format": "int32", "description": "链接类型 1:预览  2:填写页,默认2"}}, "title": "DraftUrlRequest"}, "DrafterVo": {"type": "object", "properties": {"draftOrder": {"type": "integer", "format": "int32", "description": "填写顺序，无对应任务则返回空 1-255 不同参与人不可重复"}, "fillStatus": {"type": "integer", "format": "int32", "description": "填写人填写状态 1- 未填写 2 - 填写中 3 - 已填写 4 - 拒填"}, "orgDrafter": {"description": "机构填写方信息", "$ref": "#/definitions/OrgDrafterVo"}, "psnDrafter": {"description": "个人填写方信息", "$ref": "#/definitions/PsnDrafterVo"}}, "title": "Drafter<PERSON><PERSON>"}, "FlowTemplateAccountInfo": {"type": "object", "required": ["flowTemplateOwner", "operator", "subject"], "properties": {"epaasUser": {"type": "boolean", "description": "是否是epaas用户"}, "flowTemplateOwner": {"description": "流程模板归属方", "$ref": "#/definitions/FlowTemplateOwner"}, "operator": {"description": "流程模板操作人信息", "$ref": "#/definitions/Operator"}, "subject": {"description": "流程模板创建主体", "$ref": "#/definitions/Subject"}}, "title": "FlowTemplateAccountInfo"}, "FlowTemplateConfigInfoResp": {"type": "object", "required": ["redirectUrl"], "properties": {"customComponentGroups": {"type": "array", "description": "要展示的自定义控件组id列表", "items": {"type": "string"}}, "customComponents": {"type": "array", "description": "要展示的自定义控件id列表", "items": {"type": "string"}}, "dedicatedCloudId": {"type": "string", "description": "专属云项目id"}, "fileIds": {"type": "array", "description": "模板底稿文件id列表", "items": {"type": "string"}}, "flowTemplateAccountInfo": {"description": "流程模板设置页面的账户信息", "$ref": "#/definitions/FlowTemplateAccountInfo"}, "hiddenOriginComponents": {"type": "boolean", "description": "是否隐藏原始控件，默认false true-隐藏false-不隐藏"}, "participants": {"type": "array", "description": "参与方", "items": {"$ref": "#/definitions/FlowTemplateCreateParticipant"}}, "redirectUrl": {"type": "string", "description": "创建完成后重定向地址 （最长1024字符）\t需要支持appScheme格式，提交成功后会拼接signTemplateId到地址后"}, "remarkLengthLimit": {"type": "integer", "format": "int32", "description": "备注字数上限"}, "remarkNumLimit": {"type": "integer", "format": "int32", "description": "备注条数上限"}, "uneditableFields": {"type": "array", "description": "禁止用户在页面上修改或追加的内容", "items": {"type": "string"}}}, "title": "FlowTemplateConfigInfoResp"}, "FlowTemplateCopyReq": {"type": "object", "required": ["copyToExternalOrg", "externalOrgId", "externalTransactorPsnId", "orgId", "signTemplateId", "transactorPsnId"], "properties": {"copyToExternalOrg": {"type": "boolean", "description": "是否复制到外部企业"}, "externalOrgId": {"type": "string", "description": "外部企业id"}, "externalTransactorPsnId": {"type": "string", "description": "外部经办人个人用户id"}, "orgId": {"type": "string", "description": "机构用户id\t校验有无授权manage_org_resource权限"}, "signTemplateId": {"type": "string", "description": "流程模板id"}, "transactorPsnId": {"type": "string", "description": "经办人个人用户id\t校验用户有无对应角色权限以及数据权限"}}, "title": "FlowTemplateCopyReq"}, "FlowTemplateCopyResp": {"type": "object", "properties": {"copiedSignTemplateId": {"type": "string", "description": "复制出来的流程模板id"}}, "title": "FlowTemplateCopyResp"}, "FlowTemplateCreateParticipant": {"type": "object", "required": ["participantFlag", "participantSetMode", "participantType", "participateBizType"], "properties": {"draftOrder": {"type": "integer", "format": "int32", "description": "填写顺序 1-255 不同参与人不可重复"}, "orgParticipant": {"description": "企业参与方\tparticipantSetMode=2即固定成员时生效 participantType=1时传入此对象", "$ref": "#/definitions/OrgParticipant"}, "participantFlag": {"type": "string", "description": "参与方标识，同一个模板中不可重复 会展示到模板页面上，所以建议设置为 例如甲方、乙方等容易理解的业务名词"}, "participantSetMode": {"type": "integer", "format": "int32", "description": "参与要求（参与人指定方式）1-使用模板时指定（由使用模板的人自行指定）2-固定成员（写死到模板中不可更改）3-发起人本人（使用模板的是谁就指定谁）"}, "participantType": {"type": "integer", "format": "int32", "description": "参与方类型 1-企业 2-个人"}, "participateBizType": {"type": "string", "description": "参与方式，1-填写 2-签署 用英文逗号分隔"}, "psnParticipant": {"description": "个人参与方\tparticipantSetMode=2即固定成员时生效 participantType=2时传入此对象", "$ref": "#/definitions/PsnParticipant"}, "signOrder": {"type": "integer", "format": "int32", "description": "签署顺序 1-255 不同参与人可重复"}}, "title": "FlowTemplateCreateParticipant"}, "FlowTemplateCreateUrlReq": {"type": "object", "required": ["orgId", "transactorPsnId"], "properties": {"customBizNum": {"type": "string", "description": "自定义业务编号"}, "customComponentGroups": {"type": "array", "description": "要展示的自定义控件组id列表", "items": {"type": "string"}}, "customComponents": {"type": "array", "description": "要展示的自定义控件id列表", "items": {"type": "string"}}, "dedicatedCloudId": {"type": "string", "description": "专属云项目id"}, "fileIds": {"type": "array", "description": "模板底稿文件id列表", "items": {"type": "string"}}, "hiddenOriginComponents": {"type": "boolean", "description": "是否隐藏原始控件，默认false true-隐藏false-不隐藏"}, "orgId": {"type": "string", "description": "机构用户id\t校验有无授权manage_org_resource权限"}, "participants": {"type": "array", "description": "参与方", "items": {"$ref": "#/definitions/FlowTemplateCreateParticipant"}}, "redirectUrl": {"type": "string", "description": "创建完成后重定向地址 （最长1024字符）"}, "transactorPsnId": {"type": "string", "description": "经办人个人用户id\t校验用户有无对应角色权限以及数据权限"}, "uneditableFields": {"type": "array", "description": "禁止用户在页面上修改或追加的内容 docs-待签文件 participants-参与方", "items": {"type": "string"}}}, "title": "FlowTemplateCreateUrlReq"}, "FlowTemplateCreateUrlResp": {"type": "object", "properties": {"signTemplateCreateUrl": {"type": "string", "description": "流程模板创建页面（该地址无需登录，有效期一天）"}}, "title": "FlowTemplateCreateUrlResp"}, "FlowTemplateDeleteReq": {"type": "object", "required": ["orgId", "signTemplateId", "transactorPsnId"], "properties": {"orgId": {"type": "string", "description": "机构用户id\t校验有无授权manage_org_resource权限"}, "signTemplateId": {"type": "string", "description": "流程模板id"}, "transactorPsnId": {"type": "string", "description": "经办人个人用户id\t校验用户有无对应角色权限以及数据权限"}}, "title": "FlowTemplateDeleteReq"}, "FlowTemplateDisableReq": {"type": "object", "required": ["orgId", "signTemplateId", "transactorPsnId"], "properties": {"orgId": {"type": "string", "description": "机构用户id\t校验有无授权manage_org_resource权限"}, "signTemplateId": {"type": "string", "description": "流程模板id"}, "transactorPsnId": {"type": "string", "description": "经办人个人用户id\t校验用户有无对应角色权限以及数据权限"}}, "title": "FlowTemplateDisableReq"}, "FlowTemplateEditUrlReq": {"type": "object", "required": ["orgId", "transactorPsnId"], "properties": {"customComponentGroups": {"type": "array", "description": "要展示的自定义控件组id列表", "items": {"type": "string"}}, "customComponents": {"type": "array", "description": "要展示的自定义控件id列表", "items": {"type": "string"}}, "hiddenOriginComponents": {"type": "boolean", "description": "是否隐藏原始控件，默认false true-隐藏false-不隐藏"}, "orgId": {"type": "string", "description": "机构用户id\t校验有无授权manage_org_resource权限"}, "redirectUrl": {"type": "string", "description": "创建完成后重定向地址 （最长1024字符）"}, "transactorPsnId": {"type": "string", "description": "经办人个人用户id\t校验用户有无对应角色权限以及数据权限"}, "uneditableFields": {"type": "array", "description": "禁止用户在页面上修改或追加的内容 docs-待签文件 participants-参与方", "items": {"type": "string"}}}, "title": "FlowTemplateEditUrlReq"}, "FlowTemplateEditUrlResp": {"type": "object", "properties": {"signTemplateEditUrl": {"type": "string", "description": "编辑流程模板页面链接（该地址无需登录，有效期一天）"}}, "title": "FlowTemplateEditUrlResp"}, "FlowTemplateEnableReq": {"type": "object", "required": ["orgId", "signTemplateId", "transactorPsnId"], "properties": {"orgId": {"type": "string", "description": "机构用户id\t校验有无授权manage_org_resource权限"}, "signTemplateId": {"type": "string", "description": "流程模板id"}, "transactorPsnId": {"type": "string", "description": "经办人个人用户id\t校验用户有无对应角色权限以及数据权限"}}, "title": "FlowTemplateEnableReq"}, "FlowTemplateListResp": {"type": "object", "properties": {"signTemplates": {"type": "array", "description": "流程模板列表 按更新时间倒序返回", "items": {"$ref": "#/definitions/SignTemplate"}}, "total": {"type": "integer", "format": "int64", "description": "查询结果中的流程模板总数量"}}, "title": "FlowTemplateListResp"}, "FlowTemplateOwner": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "title": "FlowTemplateOwner"}, "FlowTemplatePermissionCheckResp": {"type": "object", "properties": {"editPermission": {"type": "boolean", "description": "编辑权限\ntrue-可使用\nfalse-不可使用"}, "usingPermission": {"type": "boolean", "description": "使用权限\ntrue-可使用\nfalse-不可使用"}}, "title": "FlowTemplatePermissionCheckResp"}, "FlowTemplateResp": {"type": "object", "properties": {"flowTemplateId": {"type": "string", "description": "流程模板Id"}, "longResultUrl": {"type": "string", "description": "长链"}, "resultUrl": {"type": "string", "description": "短链"}}, "title": "FlowTemplateResp"}, "InitTaskRequest": {"type": "object", "required": ["taskId"], "properties": {"authCredentials": {"type": "string", "description": "登录凭证"}, "taskId": {"type": "string", "description": "任务id"}}, "title": "InitTaskRequest"}, "Initiator": {"type": "object", "properties": {"initiatorOrgId": {"type": "string", "description": "企业发起方id"}, "transactorPsnId": {"type": "string", "description": "企业发起方经办人id"}}, "title": "Initiator"}, "InitiatorTransactorVo": {"type": "object", "properties": {"psnAccount": {"description": "个人发起方账号", "$ref": "#/definitions/PsnAccountVo"}, "psnId": {"type": "string", "description": "个人发起方ID"}}, "title": "InitiatorTransactorVo"}, "KeywordPositionsVo": {"type": "object", "properties": {"keyword": {"type": "string", "description": "关键字"}, "positions": {"type": "array", "description": "位置列表", "items": {"$ref": "#/definitions/Position"}}, "searchResult": {"type": "boolean", "description": "是否查询到该关键字"}}, "title": "KeywordPositionsVo"}, "MergeFilesRequest": {"type": "object", "required": ["fileIds", "fileName"], "properties": {"fileIds": {"type": "array", "description": "需要合并的文件id列表", "items": {"type": "string"}}, "fileName": {"type": "string", "description": "合并后的文件名称，后缀名为.pdf"}}, "title": "MergeFilesRequest"}, "NormalSignField": {"type": "object", "properties": {"dateFormat": {"type": "string", "description": "日期格式：\n\nyyyy年MM月dd日\n\nyyyy-MM-dd\n\nyyyy/MM/dd\n\nyyyy-MM-dd HH:mm:ss"}, "mustSign": {"type": "boolean", "description": "是否必签 ，true-是,false-否"}, "sealSpecs": {"type": "integer", "format": "int32", "description": "印章规格, 1-以实际印章规格盖章, 2-自定义规格（根据签署区宽高适配"}, "sealType": {"type": "integer", "format": "int32", "description": "印章类型,  1-企业章 2-经办人 3-法定代表人章"}, "showSignDate": {"type": "integer", "format": "int32", "description": "是否显示签署日期，\n0 - 不显示\n1 - 显示"}, "signFieldStyle": {"type": "integer", "format": "int32", "description": "签署区类型，1-单页，2-骑缝"}}, "title": "NormalSignField"}, "NoticeConfigVo": {"type": "object", "properties": {"examineNotice": {"type": "boolean", "description": "是否发送审批通知, 不传 - 取noticeTypes的配置, true - 发送通知(通知给用印审批人员的通知类型，按照账号中的手机号或邮箱的填写情况进行通知), false - 不发送通知;"}, "noticeTypes": {"type": "string", "description": "通知类型,默认传空（英文逗号分隔）， 传空 - 不通知1 - 短信通知2 - 邮件通知"}}, "title": "NoticeConfigVo"}, "OpenFlowTemplateDetailResp": {"type": "object", "required": ["docs", "orgId", "participants", "signTemplateId", "signTemplateName", "signTemplateStatus"], "properties": {"attachments": {"type": "array", "description": "设置附件（无需签名的文件）信息", "items": {"$ref": "#/definitions/Attachment"}}, "copiers": {"type": "array", "description": "设置抄送方", "items": {"$ref": "#/definitions/Copier"}}, "dedicatedCloudId": {"type": "string", "description": "专属云项目id"}, "docs": {"type": "array", "description": "底稿文件信息", "items": {"$ref": "#/definitions/Doc"}}, "independentComponents": {"type": "array", "description": "不限定签署方控件", "items": {"$ref": "#/definitions/ComponentDetail"}}, "orgId": {"type": "string", "description": "机构用户id\t校验有无授权manage_org_resource权限"}, "participants": {"type": "array", "description": "参与方", "items": {"$ref": "#/definitions/Participant"}}, "signTemplateId": {"type": "string", "description": "流程模板id"}, "signTemplateName": {"type": "string", "description": "流程模板名称"}, "signTemplateStatus": {"type": "integer", "format": "int32", "description": "0-关闭 1-开启"}}, "title": "OpenFlowTemplateDetailResp"}, "Operator": {"type": "object", "properties": {"account": {"type": "string"}, "name": {"type": "string"}}, "title": "Operator"}, "Option": {"type": "object", "properties": {"optionContent": {"type": "string", "description": "选项内容"}, "optionOrder": {"type": "integer", "format": "int32", "description": "选项顺序"}, "selected": {"type": "boolean", "description": "是否默认选中"}}, "title": "Option"}, "Org": {"type": "object", "properties": {"orgId": {"type": "string", "description": "企业id"}, "orgName": {"type": "string", "description": "企业名称"}}, "title": "Org"}, "OrgDrafterVo": {"type": "object", "properties": {"orgId": {"type": "string", "description": "机构填写方ID"}, "orgName": {"type": "string", "description": "机构填写方名称"}, "transactor": {"description": "机构填写方经办人", "$ref": "#/definitions/TransactorVo"}}, "title": "OrgDrafterVo"}, "OrgParticipant": {"type": "object", "properties": {"orgId": {"type": "string", "description": "企业id"}, "orgName": {"type": "string", "description": "企业名称"}, "transactor": {"description": "企业参与方经办人", "$ref": "#/definitions/Transactor"}}, "title": "OrgParticipant"}, "Participant": {"type": "object", "required": ["components", "participantFlag", "participantId", "participantSetMode", "participantType", "participateBizType", "sealTypes", "willingnessAuthModes"], "properties": {"components": {"type": "array", "description": "控件列表（包含的参数见下方）\t按控件页码从小到大、纵坐标从大到小、横坐标从小到大的顺序", "items": {"$ref": "#/definitions/ComponentDetail"}}, "draftOrder": {"type": "integer", "format": "int32", "description": "填写顺序 1-255 不同参与人不可重复"}, "orgParticipant": {"description": "企业参与方\tparticipantSetMode=2即固定成员时生效 participantType=1时传入此对象", "$ref": "#/definitions/OrgParticipant"}, "participantFlag": {"type": "string", "description": "参与方标识，同一个模板中不可重复 会展示到模板页面上，所以建议设置为 例如甲方、乙方等容易理解的业务名词"}, "participantId": {"type": "string", "description": "参与方id，需要开发者保存，以便后续发起时使用"}, "participantSetMode": {"type": "integer", "format": "int32", "description": "参与要求（参与人指定方式）1-使用模板时指定（由使用模板的人自行指定）2-固定成员（写死到模板中不可更改）3-发起人本人（使用模板的是谁就指定谁）4-固定企业（写死到模板中不可更改）"}, "participantType": {"type": "integer", "format": "int32", "description": "参与方类型 1-企业 2-个人"}, "participateBizType": {"type": "string", "description": "参与方式，1-填写 2-签署 用英文逗号分隔"}, "psnParticipant": {"description": "个人参与方\tparticipantSetMode=2即固定成员时生效 participantType=2时传入此对象", "$ref": "#/definitions/PsnParticipant"}, "sealTypes": {"type": "string", "description": "可选签章方式，用英文逗号隔开\t1-企业章\t2-法定代表人章\t3-个人-手绘签名\t4-个人-模板章\t5-个人-AI手绘签名"}, "signOrder": {"type": "integer", "format": "int32", "description": "签署顺序 1-255 不同参与人可重复"}, "willingnessAuthModes": {"type": "string", "description": "签署人可选意愿方式，用英文逗号隔开 1-人脸认证（包含e签宝、腾讯云、支付宝三种刷脸方式 2-短信验证码 3-邮箱验证码 4-签署密码（需要签署人在e签宝设置过签署密码，纯api客户不建议使用）"}}, "title": "Participant"}, "ParticipantVo": {"type": "object", "properties": {"draftOrder": {"type": "integer", "format": "int32", "description": "填写顺序"}, "draftStatus": {"type": "integer", "format": "int32", "description": "填写状态 1-未填写 2-填写中 3-已填写 4-拒填"}, "orgDrafter": {"description": "企业填写方", "$ref": "#/definitions/企业填写方"}, "participantFlag": {"type": "string", "description": "参与方标识"}, "participantId": {"type": "string", "description": "参与方id"}, "psnDrafter": {"description": "个人填写方", "$ref": "#/definitions/个人填写方"}, "statusUpdateTime": {"type": "integer", "format": "int64", "description": "签署区状态更新时间， 当status=1时，updataTime为签署流程创建时间 当status=2时，updataTime为填写任务创建的时间 当status=3时，updataTime为填写完成时间  当status=4时，updataTime为拒填时间"}}, "title": "ParticipantVo"}, "Position": {"type": "object", "properties": {"coordinates": {"type": "array", "description": "坐标列表", "items": {"$ref": "#/definitions/Coordinate"}}, "pageNum": {"type": "integer", "format": "int32", "description": "页码"}}, "title": "Position"}, "Psn": {"type": "object", "properties": {"psnAccount": {"type": "string", "description": "个人手机号/邮箱"}, "psnId": {"type": "string", "description": "个人id"}, "psnName": {"type": "string", "description": "个人姓名"}}, "title": "Psn"}, "PsnAccountVo": {"type": "object", "properties": {"accountEmail": {"type": "string", "description": "e签宝服务个人登录邮箱"}, "accountMobile": {"type": "string", "description": "e签宝服务个人登录手机号"}}, "title": "PsnAccountVo"}, "PsnDrafterVo": {"type": "object", "properties": {"psnAccount": {"description": "个人填写方账号", "$ref": "#/definitions/PsnAccountVo"}, "psnId": {"type": "string", "description": "个人填写方id"}}, "title": "PsnDrafterVo"}, "PsnInitiatorBean": {"type": "object", "properties": {"psnId": {"type": "string", "description": "发起方的个人ID"}}, "title": "PsnInitiatorBean"}, "PsnParticipant": {"type": "object", "properties": {"psnAccount": {"type": "string", "description": "个人手机号/邮箱"}, "psnId": {"type": "string", "description": "个人id"}, "psnName": {"type": "string", "description": "个人姓名"}}, "title": "PsnParticipant"}, "QueryCertTaskAuthCredentialsRequest": {"type": "object", "required": ["loginContextId"], "properties": {"loginContextId": {"type": "string", "description": "上下文id"}}, "title": "QueryCertTaskAuthCredentialsRequest"}, "QueryTaskRequest": {"type": "object", "required": ["taskId"], "properties": {"taskId": {"type": "string", "description": "任务id"}}, "title": "QueryTaskRequest"}, "RemarkSignField": {"type": "object", "properties": {"aiCheck": {"type": "integer", "format": "int32", "description": "是否开启 AI 手写抄录校验，\n\n0 - 不开启\n\n1 - 开启 AI 校验\n\n2 - 强制 AI 校验"}, "inputType": {"type": "integer", "format": "int32", "description": "备注文字输入方式\n\n1 - 手写抄录输入\n\n2 - 键盘自由输入"}, "mustSign": {"type": "boolean", "description": "是否必签 ，true-是,false-否"}, "remarkContent": {"type": "string", "description": "预设待抄录信息\n\n"}, "remarkFontSize": {"type": "integer", "format": "int32", "description": "备注文字字号，默认值14px\n\n"}}, "title": "RemarkSignField"}, "SearchKeywordRequest": {"type": "object", "required": ["keywords"], "properties": {"keywords": {"type": "array", "description": "关键字列表", "items": {"type": "string"}}}, "title": "SearchKeywordRequest"}, "SignConfigVo": {"type": "object", "properties": {"availableSignClientTypes": {"type": "string", "description": "可选择的签署终端类型，默认值 1和2（英文逗号分隔）1 - 网页（自适配H5/PC样式）2 - 支付宝 3 - 微信小程序 4 - e签宝app"}}, "title": "SignConfigVo"}, "SignDocVo": {"type": "object", "properties": {"downloadUrl": {"type": "string", "description": "文件下载地址（合同完成拟定后才返回，30天有效）"}, "fileId": {"type": "string", "description": "文件ID"}, "fileName": {"type": "string", "description": "文件名称"}}, "title": "SignDocVo"}, "SignFlowConfigVo": {"type": "object", "properties": {"authConfig": {"description": "认证配置项", "$ref": "#/definitions/AuthConfigVo"}, "autoFinish": {"type": "boolean", "description": "所有签署方签署完成后自动完结签署流程，默认值false"}, "autoStart": {"type": "boolean", "description": "自动开启签署流程，默认值true"}, "chargeConfig": {"description": "计费配置项", "$ref": "#/definitions/ChargeConfigVo"}, "noticeConfig": {"description": "通知配置项", "$ref": "#/definitions/NoticeConfigVo"}, "notifyUrl": {"type": "string", "description": "接收回调通知的Web地址"}, "signConfig": {"description": "签署流程配置项", "$ref": "#/definitions/SignConfigVo"}, "signFlowExpireTime": {"type": "integer", "format": "int64", "description": "签署流程签署截止时间， 默认为流程创建后90天"}, "signFlowTitle": {"type": "string", "description": "签署流程主题"}}, "title": "SignFlowConfigVo"}, "SignFlowInitiatorVo": {"type": "object", "properties": {"orgId": {"type": "string", "description": "机构发起方ID"}, "orgName": {"type": "string", "description": "机构发起方名称"}, "transactor": {"description": "机构发起经办人信息", "$ref": "#/definitions/InitiatorTransactorVo"}}, "title": "SignFlowInitiatorVo"}, "SignFlowListRequest": {"type": "object", "required": ["pageNum", "pageSize", "signFlowStartTimeFrom", "signFlowStartTimeTo"], "properties": {"draftStatus": {"type": "array", "description": "流程状态列表(默认1:填写中，2:完成 ，3:撤销，4:拒填，5:过期)", "items": {"type": "integer", "format": "int32"}}, "fillStatus": {"type": "array", "description": "填写人填写状态（必须传入operator）1- 未填写 2 - 填写中 3 - 已填写 4 - 拒填", "items": {"type": "integer", "format": "int32"}}, "initiator": {"description": "发起方", "$ref": "#/definitions/Initiator"}, "operator": {"description": "填写操作人（个人填写方本人为操作人，机构填写方经办人为操作人）", "$ref": "#/definitions/个人填写方信息"}, "organization": {"description": "企业填写方", "$ref": "#/definitions/机构填写方信息"}, "pageNum": {"type": "integer", "format": "int32", "description": "页码（大于0，最小值为1）"}, "pageSize": {"type": "integer", "format": "int32", "description": "每页展示的数量（可选范围[1~100]）"}, "signFlowStartTimeFrom": {"type": "integer", "format": "int64", "description": "开始时间(流程发起时间)"}, "signFlowStartTimeTo": {"type": "integer", "format": "int64", "description": "结束时间(流程发起时间)"}}, "title": "SignFlowListRequest"}, "SignTaskResultInfoBean": {"type": "object", "properties": {"certId": {"type": "string", "description": "证书id"}, "nodeFieldId": {"type": "string", "description": "签署区id"}, "signLogId": {"type": "string", "description": "签署日志id"}}, "title": "SignTaskResultInfoBean"}, "SignTemplate": {"type": "object", "properties": {"createTime": {"type": "integer", "format": "int64", "description": "创建时间（时间戳）"}, "signTemplateId": {"type": "string", "description": "流程模板id"}, "signTemplateName": {"type": "string", "description": "流程模板名称"}, "signTemplateSource": {"type": "integer", "format": "int32", "description": "1-<PERSON><PERSON><PERSON>, 2-薪福通"}, "status": {"type": "integer", "format": "int32", "description": "0-关闭\n1-开启"}, "updateTime": {"type": "integer", "format": "int64", "description": "更新时间（时间戳）"}}, "title": "SignTemplate"}, "SignUrlReq": {"type": "object", "required": ["cooperationId"], "properties": {"clientType": {"type": "string", "description": "客户端,PC或者H5"}, "cooperationId": {"type": "string", "description": "cooperationId"}, "firstRequest": {"type": "boolean", "description": "true为填写完成第一次请求,false为再次进入填写页面请求"}, "redirectDelayTime": {"type": "integer", "format": "int32", "description": "redirectDelayTime"}, "redirectUrl": {"type": "string", "description": "redirectUrl"}}, "title": "SignUrlReq"}, "Subject": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "title": "Subject"}, "TableRow": {"type": "object", "properties": {"row": {"type": "object", "additionalProperties": {"type": "object"}}}, "title": "TableRow"}, "Transactor": {"type": "object", "properties": {"transactorName": {"type": "string", "description": "经办人姓名"}, "transactorPsnAccount": {"type": "string", "description": "经办人手机号/邮箱"}, "transactorPsnId": {"type": "string", "description": "经办人个人id"}}, "title": "Transactor"}, "TransactorBean": {"type": "object", "properties": {"transactorName": {"type": "string", "description": "经办人姓名"}, "transactorPsnAccount": {"type": "string", "description": "经办人手机号/邮箱"}, "transactorPsnId": {"type": "string", "description": "经办人个人id"}}, "title": "TransactorBean"}, "TransactorVo": {"type": "object", "properties": {"psnAccount": {"description": "经办人个人账号", "$ref": "#/definitions/PsnAccountVo"}, "psnId": {"type": "string", "description": "经办人个人id"}}, "title": "TransactorVo"}, "UrgeFillingRequest": {"type": "object", "properties": {"noticeTypes": {"type": "string", "description": "通知方式，英文逗号分割，1-短信，2-邮件"}, "urgedOperator": {"description": "被催签人的账号信息", "$ref": "#/definitions/UrgedOperatorBean"}}, "title": "UrgeFillingRequest"}, "UrgedOperatorBean": {"type": "object", "properties": {"psnAccount": {"type": "string", "description": "被催签人账号 手机号/邮箱，为空表示：催签当前轮到签署但还未签署的所有签署人"}, "psnId": {"type": "string", "description": "被催签人账号id，为空表示：催签当前轮到签署但还未签署的所有签署人"}}, "title": "UrgedOperatorBean"}, "个人填写方": {"type": "object", "properties": {"psnAccount": {"type": "string", "description": "个人手机号/邮箱"}, "psnId": {"type": "string", "description": "个人ID"}, "psnName": {"type": "string", "description": "个人姓名"}}, "title": "个人填写方"}, "个人填写方信息": {"type": "object", "properties": {"psnAccount": {"type": "string", "description": "个人填写方账号"}, "psnId": {"type": "string", "description": "个人填写方id"}}, "title": "个人填写方信息"}, "企业参与方经办人": {"type": "object", "properties": {"transactorName": {"type": "string", "description": "经办人姓名"}, "transactorPsnAccount": {"type": "string", "description": "经办人手机号/邮箱"}, "transactorPsnId": {"type": "string", "description": "经办人ID"}}, "title": "企业参与方经办人"}, "企业填写方": {"type": "object", "properties": {"orgId": {"type": "string", "description": "企业id"}, "orgName": {"type": "string", "description": "企业名称"}, "transactor": {"description": "企业参与方经办人", "$ref": "#/definitions/企业参与方经办人"}}, "title": "企业填写方"}, "创建任务": {"type": "object", "properties": {"taskId": {"type": "string", "description": "任务id"}, "taskLongUrl": {"type": "string", "description": "任务长链地址"}, "taskUrl": {"type": "string", "description": "任务操作地址"}}, "title": "创建任务"}, "创建流程 附件对象": {"type": "object", "properties": {"fileId": {"type": "string", "description": "附件文件ID"}, "fileName": {"type": "string", "description": "附件文件名称"}}, "title": "创建流程 附件对象"}, "创建流程抄送人对象": {"type": "object", "properties": {"copierOrgInfo": {"description": "机构抄送方信息", "$ref": "#/definitions/CopierOrgInfo"}, "copierPsnInfo": {"description": "个人抄送方信息", "$ref": "#/definitions/CopierPsnInfo"}}, "title": "创建流程抄送人对象"}, "初始化任务返回值": {"type": "object", "properties": {"certPsnMismatchVo": {"description": "个人信息不一致返回值", "$ref": "#/definitions/CertPsnMismatchVo"}, "status": {"type": "integer", "format": "int32", "description": "任务状态 1-初始化, 2-等待制证 3-制证完成 4-制证失败"}, "success": {"type": "boolean", "description": "是否成功"}, "targetUrl": {"type": "string", "description": "下一步地址"}}, "title": "初始化任务返回值"}, "制证返回值": {"type": "object", "properties": {"algorithm": {"type": "string", "description": "算法，RSA,SM2"}, "caRoute": {"type": "string", "description": "申领成功的证书通道名称"}, "certId": {"type": "string", "description": "证书id"}, "encCert": {"type": "string", "description": "加密证书"}, "endDate": {"type": "integer", "format": "int64", "description": "证书结束时间"}, "ext": {"type": "string", "description": "扩展信息"}, "signCert": {"type": "string", "description": "签名证书base64"}, "signSn": {"type": "string", "description": "签名证书SN"}, "startDate": {"type": "integer", "format": "int64", "description": "证书开始时间"}}, "title": "制证返回值"}, "参与人信息": {"type": "object", "required": ["subjectName", "subjectType"], "properties": {"account": {"type": "string", "description": "邮箱/手机号账号"}, "accountName": {"type": "string", "description": "账号对应人姓名"}, "accountNick": {"type": "string", "description": "账号对应人昵称"}, "accountOid": {"type": "string", "description": "账号oid"}, "accountRealName": {"type": "boolean", "description": "参与人是否实名, 默认为false"}, "comment": {"type": "string", "description": "账号备注"}, "preFillValues": {"type": "object", "description": "预填内容"}, "registerSource": {"type": "string", "description": "用户来源一般来源clientId"}, "subTaskBizId": {"type": "string", "description": "三方业务id"}, "subTaskBizType": {"type": "integer", "format": "int32", "description": "三方业务类型"}, "subTaskName": {"type": "string", "description": "子任务名称，批量时传入"}, "subjectId": {"type": "string", "description": "主体oid"}, "subjectName": {"type": "string", "description": "主体名称"}, "subjectRealName": {"type": "boolean", "description": "主体是否实名 默认false"}, "subjectType": {"type": "integer", "format": "int32", "description": "主体类型 0-个人 1-企业"}}, "title": "参与人信息"}, "参与文件": {"type": "object", "required": ["fileId", "fileType"], "properties": {"contractNo": {"type": "string", "description": "合同编号"}, "contractNoRule": {"type": "string", "description": "合同编号规则"}, "contractNoType": {"type": "integer", "format": "int32", "description": "合同编号类型"}, "fileId": {"type": "string", "description": "文件id"}, "fileName": {"type": "string", "description": "文件名称"}, "fileSecret": {"type": "boolean", "description": "文件是否保密"}, "fileType": {"type": "integer", "format": "int32", "description": "文件类型，1-合并文件 2-附件"}, "from": {"type": "integer", "format": "int32", "description": "文件来自 1-模板文件 2-合同文件 3-动态模板文件"}, "order": {"type": "integer", "format": "int32", "description": "文件顺序"}, "sourceFileName": {"type": "string", "description": "源文件名"}}, "title": "参与文件"}, "发起签署前-单个参与人二要素校验响应参数": {"type": "object", "properties": {"accountName": {"type": "string", "description": "已实名用户脱敏显示的账号姓名"}, "checkName": {"type": "boolean", "description": "姓名和手机号/邮箱账号实名信息是否一致"}, "name": {"type": "string", "description": "前端传入的姓名"}, "uniqueId": {"type": "string", "description": "前端传入的手机号/邮箱"}}, "title": "发起签署前-单个参与人二要素校验响应参数"}, "发起签署前-单个参与人二要素校验请求参数": {"type": "object", "properties": {"name": {"type": "string", "description": "姓名"}, "uniqueId": {"type": "string", "description": "手机号/邮箱"}}, "title": "发起签署前-单个参与人二要素校验请求参数"}, "发起签署前-批量参与人二要素校验响应参数": {"type": "object", "required": ["status"], "properties": {"flowId": {"type": "string", "description": "签署流程id"}, "longUrl": {"type": "string", "description": "签署长链接"}, "shortUrl": {"type": "string", "description": "签署短链"}, "signers": {"type": "array", "description": "账号列表", "items": {"$ref": "#/definitions/CheckNamesResponseBean"}}, "status": {"type": "integer", "format": "int32", "description": "1.发起中,前端轮询,2.无需获取,走原逻辑,3.获取成功"}}, "title": "发起签署前-批量参与人二要素校验响应参数"}, "发起签署前-批量参与人二要素校验请求参数": {"type": "object", "properties": {"signers": {"type": "array", "description": "账号列表", "items": {"$ref": "#/definitions/CheckNamesRequestBean"}}}, "title": "发起签署前-批量参与人二要素校验请求参数"}, "合同拟定以及签署流程状态返回结果": {"type": "object", "properties": {"draftStatus": {"type": "integer", "format": "int32", "description": "拟定状态 1-拟定中 2-完成 3-撤销 4-拒填 5-过期"}, "rescissionStatus": {"type": "integer", "format": "int32", "description": "签署流程的解约状态 0 - 未解约 1 - 解约中 2 - 部分解约 3 - 已解约"}, "signFlowStatus": {"type": "integer", "format": "int32", "description": "签署流程状态 0-草稿 1-签署中 2-完成 3-撤销 5-过期（签署截至日期到期后触发） 7-拒签"}}, "title": "合同拟定以及签署流程状态返回结果"}, "合同拟定详情返回参数": {"type": "object", "properties": {"docs": {"type": "array", "description": "签署流程配置", "items": {"$ref": "#/definitions/SignDocVo"}}, "draftEndTime": {"type": "integer", "format": "int64", "description": "拟定流程结束时间，时间戳"}, "draftStartTime": {"type": "integer", "format": "int64", "description": "拟定流程开启时间，时间戳"}, "draftStatus": {"type": "integer", "format": "int32", "description": "拟定流程状态 1-拟定中 2-完成 3-撤销 4-拒填 5-过期"}, "drafters": {"type": "array", "description": "填写方信息", "items": {"$ref": "#/definitions/ParticipantVo"}}, "signFlowConfig": {"description": "签署流程配置", "$ref": "#/definitions/SignFlowConfigVo"}, "signFlowInitiator": {"description": "签署流程发起方", "$ref": "#/definitions/签署流程发起方信息"}, "signTemplateId": {"type": "string", "description": "签署流程模板id"}}, "title": "合同拟定详情返回参数"}, "合同相关配置项": {"type": "object", "properties": {"allowToRescind": {"type": "boolean", "description": "是否允许解约, 默认为true"}}, "title": "合同相关配置项"}, "合并文件返回参数": {"type": "object", "properties": {"mergedFileId": {"type": "string", "description": "合并后的文件ID"}}, "title": "合并文件返回参数"}, "填写链接返回结果": {"type": "object", "properties": {"draftShortUrl": {"type": "string", "description": "短链地址（30天有效）"}, "draftUrl": {"type": "string", "description": "长链地址(永久有效)"}}, "title": "填写链接返回结果"}, "延期证书返回值": {"type": "object", "properties": {"algorithm": {"type": "string", "description": "算法，RSA,SM2"}, "certId": {"type": "string", "description": "证书id"}, "encCert": {"type": "string", "description": "加密证书"}, "endDate": {"type": "integer", "format": "int64", "description": "证书结束时间"}, "ext": {"type": "string", "description": "扩展信息"}, "signCert": {"type": "string", "description": "签名证书base64"}, "signSn": {"type": "string", "description": "签名证书SN"}, "startDate": {"type": "integer", "format": "int64", "description": "证书开始时间"}}, "title": "延期证书返回值"}, "搜索关键字坐标返回参数": {"type": "object", "properties": {"keywordPositions": {"type": "array", "description": "关键字位置列表", "items": {"$ref": "#/definitions/KeywordPositionsVo"}}}, "title": "搜索关键字坐标返回参数"}, "撤销合同拟定返回参数": {"type": "object", "properties": {"bizContext": {"type": "object"}}, "title": "撤销合同拟定返回参数"}, "更新证书返回值": {"type": "object", "properties": {"algorithm": {"type": "string", "description": "算法，RSA,SM2"}, "certId": {"type": "string", "description": "证书id"}, "encCert": {"type": "string", "description": "加密证书"}, "endDate": {"type": "integer", "format": "int64", "description": "证书结束时间"}, "ext": {"type": "string", "description": "扩展信息"}, "signCert": {"type": "string", "description": "签名证书base64"}, "signSn": {"type": "string", "description": "签名证书SN"}, "startDate": {"type": "integer", "format": "int64", "description": "证书开始时间"}}, "title": "更新证书返回值"}, "机构发起方的发起人": {"type": "object", "properties": {"psnId": {"type": "string", "description": "发起人的个人ID"}}, "title": "机构发起方的发起人"}, "机构填写方信息": {"type": "object", "properties": {"orgId": {"type": "string", "description": "机构填写方账号ID"}, "orgName": {"type": "string", "description": "机构填写方名称"}}, "title": "机构填写方信息"}, "查询任务返回值": {"type": "object", "properties": {"certPsnMismatchVo": {"description": "个人信息不一致返回值", "$ref": "#/definitions/CertPsnMismatchVo"}, "createCertFlag": {"type": "boolean", "description": "是否同步制证"}, "failReason": {"type": "string", "description": "制证失败原因"}, "redirectUrl": {"type": "string", "description": "完成后重定向地址"}, "status": {"type": "integer", "format": "int32", "description": "任务状态 1-初始化, 2-等待制证 3-制证完成 4-制证失败"}, "success": {"type": "boolean", "description": "是否成功"}, "taskId": {"type": "string", "description": "任务id"}}, "title": "查询任务返回值"}, "查询登录凭证返回值": {"type": "object", "properties": {"authCredentials": {"type": "string", "description": "登录凭证"}}, "title": "查询登录凭证返回值"}, "根据任务id查询返回值": {"type": "object", "properties": {"cert": {"type": "array", "description": "证书信息", "items": {"$ref": "#/definitions/ApplyCertVo"}}, "failReason": {"type": "string", "description": "制证失败原因"}, "status": {"type": "integer", "format": "int32", "description": "任务状态 1-初始化, 2-等待制证 3-制证完成 4-制证失败"}, "taskId": {"type": "string", "description": "任务id"}}, "title": "根据任务id查询返回值"}, "模板签署设置校验请求参数": {"type": "object", "required": ["participants"], "properties": {"participants": {"type": "array", "description": "参与方列表", "items": {"$ref": "#/definitions/流程参与方"}}}, "title": "模板签署设置校验请求参数"}, "流程列表返回参数": {"type": "object", "properties": {"draftInfos": {"type": "array", "description": "拟定流程信息列表", "items": {"$ref": "#/definitions/DraftInfoVo"}}, "total": {"type": "integer", "format": "int64", "description": "列表总数"}}, "title": "流程列表返回参数"}, "流程参与方": {"type": "object", "required": ["participantLabel", "role", "roleSet"], "properties": {"assignedSubjectId": {"type": "string", "description": "参与方企业主体id"}, "assignedSubjectName": {"type": "string", "description": "参与方企业主体名称"}, "fillOrder": {"type": "integer", "format": "int32", "description": "填写顺序"}, "instances": {"type": "array", "description": "参与方实例化", "items": {"$ref": "#/definitions/参与人信息"}}, "participantId": {"type": "string", "description": "参与方id"}, "participantLabel": {"type": "string", "description": "参与方名称"}, "participantSubjectType": {"type": "integer", "format": "int32", "description": "参与方主体类型 0-个人 1-企业 2-待定"}, "role": {"type": "string", "description": "角色类型， 逗号分隔 1-填写方 2-填写审批 3-签署方"}, "roleSet": {"type": "integer", "format": "int32", "description": "协作方角色设置 0-指定用户 1-发起人指定 2-发起人自己 3-固定企业"}, "sealType": {"type": "string", "description": "印章类型，逗号分割，0-手绘印章，1-模版印章，2-ai手绘，为空或0,1不限制"}, "sharable": {"type": "boolean", "description": "是否扫码签参与方"}, "shareMax": {"type": "integer", "format": "int32", "description": "扫码参与上限"}, "signOrder": {"type": "integer", "format": "int32", "description": "签署顺序"}, "signRequirements": {"type": "string", "description": "签署要求，逗号分隔 1-企业章 2-经办人 3-法定代表人章"}, "type": {"type": "integer", "format": "int32"}, "willTypes": {"type": "array", "description": "指定意愿方式, FACE-人脸认证,CODE_SMS-短信认证,EMAIL-邮箱认证,SIGN_PWD-密码认证,FACE_AUDIO_VIDEO_DUAL-视频认证", "items": {"type": "string"}}}, "title": "流程参与方"}, "流程发起业务模型": {"type": "object", "required": ["backInfo", "openId", "participants", "taskName"], "properties": {"backInfo": {"description": "返回到流程模板设置页面地址需要的参数信息", "$ref": "#/definitions/BackInfo"}, "ccs": {"type": "array", "description": "抄送人列表", "items": {"$ref": "#/definitions/参与人信息"}}, "epaasTag": {"type": "boolean", "description": "epaas标记"}, "files": {"type": "array", "description": "文件列表,包括合同文件和附件", "items": {"$ref": "#/definitions/参与文件"}}, "flowTemplateId": {"type": "string", "description": "流程模板id"}, "openId": {"type": "string", "description": "openId"}, "participants": {"type": "array", "description": "参与方列表", "items": {"$ref": "#/definitions/流程参与方"}}, "remarkList": {"type": "array", "description": "发起备注", "items": {"type": "string"}}, "shared": {"type": "boolean"}, "signTemplateName": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "taskName": {"type": "string", "description": "任务名称，不支持特殊字符"}}, "title": "流程发起业务模型"}, "流程模板创建流程-个人参与方信息对象": {"type": "object", "properties": {"psnAccount": {"type": "string", "description": "个人手机号/邮箱"}, "psnId": {"type": "string", "description": "个人id"}, "psnName": {"type": "string", "description": "个人姓名"}}, "title": "流程模板创建流程-个人参与方信息对象"}, "流程模板创建流程-参与方信息对象": {"type": "object", "properties": {"orgParticipant": {"description": "企业参与方", "$ref": "#/definitions/流程模板创建流程-机构参与方信息对象"}, "participantId": {"type": "string", "description": "参与方id"}, "psnParticipant": {"description": "个人参与方", "$ref": "#/definitions/流程模板创建流程-个人参与方信息对象"}}, "title": "流程模板创建流程-参与方信息对象"}, "流程模板创建流程-机构参与方信息对象": {"type": "object", "properties": {"orgId": {"type": "string", "description": "企业ID"}, "orgName": {"type": "string", "description": "企业名称"}, "transactor": {"description": "企业参与方经办人", "$ref": "#/definitions/TransactorBean"}}, "title": "流程模板创建流程-机构参与方信息对象"}, "流程模板创建流程-预填控件对象": {"type": "object", "properties": {"componentId": {"type": "string", "description": "控件id"}, "componentKey": {"type": "string", "description": "控件key，（控件id和控件key二选一，优先取控件id）"}, "componentValue": {"type": "string", "description": "控件填充值"}, "fileId": {"type": "string", "description": "控件所属文件ID"}}, "title": "流程模板创建流程-预填控件对象"}, "流程模板创建流程发起方": {"type": "object", "properties": {"initialRemarks": {"type": "array", "description": "发起方备注", "items": {"type": "string"}}, "orgId": {"type": "string", "description": "机构发起方的ID"}, "transactor": {"description": "机构发起方的经办人", "$ref": "#/definitions/PsnInitiatorBean"}}, "title": "流程模板创建流程发起方"}, "流程模板创建流程流程配置": {"type": "object", "properties": {"authConfig": {"description": "认证配置项", "$ref": "#/definitions/签署流程 认证配置项对象"}, "autoFinish": {"type": "boolean", "description": "所有签署方签署完成后自动完结签署流程，默认值false"}, "autoStart": {"type": "boolean", "description": "自动开启签署流程，默认值true"}, "chargeConfig": {"description": "计费配置项", "$ref": "#/definitions/签署流程 计费配置项"}, "contractConfig": {"description": "合同相关配置项", "$ref": "#/definitions/合同相关配置项"}, "noticeConfig": {"description": "通知配置项", "$ref": "#/definitions/签署流程 通知配置项"}, "notifyUrl": {"type": "string", "description": "接收回调通知的Web地址"}, "signConfig": {"description": "签署流程配置项", "$ref": "#/definitions/签署配置项"}, "signFlowExpireTime": {"type": "integer", "format": "int64", "description": "签署流程签署截止时间， 默认为流程创建后90天"}, "signFlowTitle": {"type": "string", "description": "签署流程主题"}}, "title": "流程模板创建流程流程配置"}, "签署流程 计费配置项": {"type": "object", "properties": {"chargeMode": {"type": "integer", "format": "int32", "description": "计费模式 默认0-平台方付费，1-发起方付费，2-签署方付费"}, "orderType": {"type": "string", "description": "订单类型 DISTRIBUTION-渠道套餐"}}, "title": "签署流程 计费配置项"}, "签署流程 认证配置项对象": {"type": "object", "properties": {"orgAvailableAuthModes": {"type": "array", "description": "实名认证页面中机构实名认证可选项 ORG_BANK_TRANSFER组织机构对公账户打款认证 ORG_ZM_AUTHORIZE 企业芝麻认证 ORG_LEGAL_AUTHORIZE 组织机构法定代表人授权书签署认证", "items": {"type": "string"}}, "psnAvailableAuthModes": {"type": "array", "description": "实名认证页面中个人实名认证可选项 PSN_BANK4_AUTHCODE个人银行卡四要素认证PSN_TELECOM_AUTHCODE个人运营商三要素认证PSN_FACEAUTH_BYURL个人刷脸认证", "items": {"type": "string"}}}, "title": "签署流程 认证配置项对象"}, "签署流程 通知配置项": {"type": "object", "properties": {"examineNotice": {"type": "boolean", "description": "是否发送审批通知, 不传 - 取noticeTypes的配置, true - 发送通知(通知给用印审批人员的通知类型，按照账号中的手机号或邮箱的填写情况进行通知), false - 不发送通知;"}, "noticeTypes": {"type": "string", "description": "通知类型, 传空 - 不通知1 - 短信通知2 - 邮件通知"}}, "title": "签署流程 通知配置项"}, "签署流程发起方信息": {"type": "object", "properties": {"orgId": {"type": "string", "description": "机构发起方的ID"}, "transactor": {"description": "机构发起方的发起人", "$ref": "#/definitions/机构发起方的发起人"}}, "title": "签署流程发起方信息"}, "签署配置项": {"type": "object", "properties": {"autoFillAndSubmit": {"type": "boolean", "description": "若某参与方需要填写的必填内容已经预填写完成（即控件均已传入预填值），是否自动跳过该参与方的填写步骤，且不会发送消息通知（也无法获取填写页面链接）"}, "availableSignClientTypes": {"type": "string", "description": "可选择的签署终端类型，默认值 1和2（英文逗号分隔）1 - 网页（自适配H5/PC样式）2 - 支付宝"}, "editComponentValue": {"type": "boolean", "description": "预填值是否允许修改"}}, "title": "签署配置项"}, "通用返回结果": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"type": "object", "description": "业务信息"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果"}, "通用返回结果«DedicatedCloudHashSignResponse»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/DedicatedCloudHashSignResponse"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«DedicatedCloudHashSignResponse»"}, "通用返回结果«DedicatedCloudPlatformHashSignResponse»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/DedicatedCloudPlatformHashSignResponse"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«DedicatedCloudPlatformHashSignResponse»"}, "通用返回结果«DedicatedCloudVerifyResponse»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/DedicatedCloudVerifyResponse"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«DedicatedCloudVerifyResponse»"}, "通用返回结果«FlowTemplateConfigInfoResp»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/FlowTemplateConfigInfoResp"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«FlowTemplateConfigInfoResp»"}, "通用返回结果«FlowTemplateCopyResp»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/FlowTemplateCopyResp"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«FlowTemplateCopyResp»"}, "通用返回结果«FlowTemplateCreateUrlResp»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/FlowTemplateCreateUrlResp"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«FlowTemplateCreateUrlResp»"}, "通用返回结果«FlowTemplateEditUrlResp»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/FlowTemplateEditUrlResp"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«FlowTemplateEditUrlResp»"}, "通用返回结果«FlowTemplateListResp»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/FlowTemplateListResp"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«FlowTemplateListResp»"}, "通用返回结果«FlowTemplatePermissionCheckResp»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/FlowTemplatePermissionCheckResp"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«FlowTemplatePermissionCheckResp»"}, "通用返回结果«FlowTemplateResp»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/FlowTemplateResp"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«FlowTemplateResp»"}, "通用返回结果«OpenFlowTemplateDetailResp»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/OpenFlowTemplateDetailResp"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«OpenFlowTemplateDetailResp»"}, "通用返回结果«boolean»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"type": "boolean", "description": "业务信息"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«boolean»"}, "通用返回结果«创建任务»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/创建任务"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«创建任务»"}, "通用返回结果«初始化任务返回值»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/初始化任务返回值"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«初始化任务返回值»"}, "通用返回结果«制证返回值»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/制证返回值"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«制证返回值»"}, "通用返回结果«发起签署前-批量参与人二要素校验响应参数»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/发起签署前-批量参与人二要素校验响应参数"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«发起签署前-批量参与人二要素校验响应参数»"}, "通用返回结果«合同拟定以及签署流程状态返回结果»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/合同拟定以及签署流程状态返回结果"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«合同拟定以及签署流程状态返回结果»"}, "通用返回结果«合同拟定详情返回参数»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/合同拟定详情返回参数"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«合同拟定详情返回参数»"}, "通用返回结果«合并文件返回参数»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/合并文件返回参数"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«合并文件返回参数»"}, "通用返回结果«填写链接返回结果»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/填写链接返回结果"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«填写链接返回结果»"}, "通用返回结果«延期证书返回值»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/延期证书返回值"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«延期证书返回值»"}, "通用返回结果«搜索关键字坐标返回参数»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/搜索关键字坐标返回参数"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«搜索关键字坐标返回参数»"}, "通用返回结果«撤销合同拟定返回参数»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/撤销合同拟定返回参数"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«撤销合同拟定返回参数»"}, "通用返回结果«更新证书返回值»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/更新证书返回值"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«更新证书返回值»"}, "通用返回结果«查询任务返回值»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/查询任务返回值"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«查询任务返回值»"}, "通用返回结果«查询登录凭证返回值»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/查询登录凭证返回值"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«查询登录凭证返回值»"}, "通用返回结果«根据任务id查询返回值»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/根据任务id查询返回值"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«根据任务id查询返回值»"}, "通用返回结果«流程列表返回参数»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/流程列表返回参数"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«流程列表返回参数»"}, "通用返回结果«流程发起业务模型»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/流程发起业务模型"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«流程发起业务模型»"}, "通用返回结果«通过流程模板创建合同拟定和签署流程返回参数»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "错误码"}, "data": {"description": "业务信息", "$ref": "#/definitions/通过流程模板创建合同拟定和签署流程返回参数"}, "message": {"type": "string", "description": "错误信息"}}, "title": "通用返回结果«通过流程模板创建合同拟定和签署流程返回参数»"}, "通过流程模板创建合同拟定和签署流程请求参数": {"type": "object", "properties": {"addAttachments": {"type": "array", "description": "添加附件（无需签名的文件）信息", "items": {"$ref": "#/definitions/创建流程 附件对象"}}, "addCopiers": {"type": "array", "description": "添加抄送方", "items": {"$ref": "#/definitions/创建流程抄送人对象"}}, "components": {"type": "array", "description": "控件列表", "items": {"$ref": "#/definitions/流程模板创建流程-预填控件对象"}}, "participants": {"type": "array", "description": "参与人配置", "items": {"$ref": "#/definitions/流程模板创建流程-参与方信息对象"}}, "signFlowConfig": {"description": "签署流程配置", "$ref": "#/definitions/流程模板创建流程流程配置"}, "signFlowInitiator": {"description": "签署流程发起方", "$ref": "#/definitions/流程模板创建流程发起方"}, "signTemplateId": {"type": "string", "description": "流程模板id"}}, "title": "通过流程模板创建合同拟定和签署流程请求参数"}, "通过流程模板创建合同拟定和签署流程返回参数": {"type": "object", "properties": {"signFlowId": {"type": "string", "description": "签署流程id"}}, "title": "通过流程模板创建合同拟定和签署流程返回参数"}, "重定向配置项": {"type": "object", "properties": {"redirectDelayTime": {"type": "integer", "format": "int32", "description": "操作完成后页面重定向跳转延迟时间，单位为秒，默认3秒"}, "redirectUrl": {"type": "string", "description": "重定向地址"}}, "title": "重定向配置项"}}}