# 面向大模型的HttpRunner测试用例标准规范

## 1. 规范概述

### 1.1 目标
建立统一的测试用例编写标准，确保测试用例能够被大模型准确理解并自动生成HttpRunner集成测试代码，同时通过MCP工具实现数据生成和接口调用的自动化。

### 1.2 适用范围
- 所有API接口测试用例
- 业务流程测试用例
- 数据驱动测试用例
- 端到端测试场景

## 2. 用例结构规范

### 2.1 基础信息结构
```
【用例标题】: {动作}_{对象}
【测试类型】: [功能测试/接口测试/流程测试/异常测试]
【优先级】: [P0/P1/P2/P3]
【依赖关系】: [前置用例ID或无]
【标签】: [smoke/regression/api等]
```

### 2.2 详细描述结构
```
【数据准备】: 说明需要哪些测试数据及其特征
【执行步骤】: 详细的操作步骤，包含预期结果
    【预期结果】: 每个步骤的预期输出和最终验证点
【清理操作】: 测试后的数据清理要求
```

## 3. 执行步骤编写规范

### 3.1 步骤描述原则
- **原子性**: 每个步骤只做一件事
- **明确性**: 使用准确的动词和名词
- **完整性**: 包含所有必要的参数信息
- **可验证性**: 每个步骤都有明确的预期结果

### 3.2 步骤编写模板
```
步骤{序号}: {动作动词} + {操作对象} + {关键参数} + {预期结果}

### 3.3 关键动词标准化

| 动作类型 | 标准动词 | 说明 |
|---------|---------|------|
| 数据操作 | 创建/生成/删除/更新/查询 | 明确数据操作类型 |
| 接口调用 | 调用/请求/发送 | API接口操作 |
| 验证检查 | 验证/检查/确认 | 结果验证操作 |
| 数据处理 | 提取/解析/转换 | 数据处理操作 |
| 等待操作 | 等待/延时 | 时间相关操作 |

## 4. 参数描述规范

### 4.1 参数完整性要求
- **必填参数**: 明确标注所有必填参数及其值
- **可选参数**: 说明使用场景和默认值
- **动态参数**: 说明参数来源（前置步骤、MCP生成等）
- **枚举参数**: 列出所有可能值及其含义

### 4.2 参数描述格式
```
参数名称: {参数值/来源} (类型) [必填/可选] - 参数说明

示例：
- 手机号: ${生成随机手机号} (string) [必填] - 用户注册手机号
- 证书类型: "个人" (string) [必填] - 证书类型，可选值：个人/企业
- 有效期: 365 (int) [可选] - 证书有效天数，默认365天
- 用户ID: ${步骤1.响应.userId} (string) [必填] - 从注册接口获取的用户ID
```

## 5. MCP工具集成规范

### 5.1 MCP方法命名规范
```
{业务域}_{操作类型}_{对象}

示例：
- 用户_创建_个人用户
- 证书_申请_企业证书
- 订单_查询_支付状态
- 数据_生成_随机手机号
```

### 5.2 MCP方法描述要求
```yaml
方法名: 用户_创建_个人用户
描述: 创建一个个人用户，包含完整的用户信息
输入参数:
  - 用户类型: 固定为"个人"
  - 手机号: 可选，不提供则自动生成
  - 身份证号: 可选，不提供则自动生成
  - 姓名: 可选，不提供则自动生成
输出参数:
  - 用户ID: 创建成功的用户唯一标识
  - 手机号: 用户手机号
  - 身份证号: 用户身份证号
  - 姓名: 用户姓名
使用场景: 需要个人用户进行后续测试时使用
```

### 5.3 数据关联规范
```
【数据流转标记】:
- 输入标记: ${MCP方法名.参数名}
- 输出标记: ${步骤序号.响应字段名}
- 全局变量: ${变量名}

示例：
步骤1: 调用${用户_创建_个人用户}，生成个人用户数据
步骤2: 调用证书申请接口，使用用户ID=${步骤1.用户ID}，手机号=${步骤1.手机号}
```

## 6. 用例分类与模板

### 6.1 单接口测试用例模板
```
【用例标题】: 测试{接口名}接口的{具体场景}
【测试类型】: 接口测试
【测试目标】: 验证{接口名}接口在{特定条件}下的{预期行为}

【执行步骤】:
步骤1: 准备测试数据 - 调用${MCP方法}生成{数据类型}
步骤2: 调用{接口名}接口 - 使用{具体参数}，预期{具体结果}
步骤3: 验证响应结果 - 检查{具体验证点}

【预期结果】:
- 接口返回状态码: {状态码}
- 响应数据格式: {数据结构}
- 业务逻辑验证: {业务规则}
```

### 6.2 业务流程测试用例模板
```
【用例标识】: TC_FLOW_{业务流程}_{场景}
【用例标题】: {业务流程名称}完整流程测试
【测试类型】: 流程测试
【测试目标】: 验证{业务流程}的端到端执行

【业务场景】: {详细的业务背景描述}

【执行步骤】:
步骤1: 数据准备阶段
  - 调用${MCP方法1}生成{数据1}
  - 调用${MCP方法2}生成{数据2}
  
步骤2: 业务执行阶段
  - 调用{接口1}，使用{参数}，预期{结果}
  - 调用{接口2}，使用${步骤2.响应.字段}，预期{结果}
  
步骤3: 结果验证阶段
  - 调用{查询接口}验证{业务状态}
  - 检查{数据一致性}

【数据关联图】:
{数据1} → {接口1} → {中间结果} → {接口2} → {最终结果}
```

### 6.3 异常测试用例模板
```
【用例标识】: TC_ERROR_{模块}_{异常类型}
【用例标题】: 测试{异常场景}的错误处理
【测试类型】: 异常测试
【测试目标】: 验证系统在{异常条件}下的错误处理机制

【异常触发条件】: {详细描述如何触发异常}

【执行步骤】:
步骤1: 构造异常数据 - {具体的异常数据构造方法}
步骤2: 执行异常操作 - {具体的异常操作}
步骤3: 验证错误响应 - {预期的错误信息和状态码}

【预期结果】:
- 错误状态码: {具体状态码}
- 错误信息: {具体错误描述}
- 系统状态: {系统应保持的状态}
```

## 7. 大模型识别优化规范

### 7.1 关键词标准化
```yaml
动作关键词:
  创建类: [创建, 新建, 生成, 注册, 申请]
  查询类: [查询, 获取, 检索, 搜索, 查看]
  更新类: [更新, 修改, 编辑, 变更]
  删除类: [删除, 移除, 注销, 取消]
  验证类: [验证, 检查, 确认, 校验]

对象关键词:
  用户类: [用户, 个人, 企业, 账户]
  证书类: [证书, 数字证书, 签名证书]
  订单类: [订单, 支付, 交易]
  
状态关键词:
  成功类: [成功, 完成, 通过, 有效]
  失败类: [失败, 错误, 无效, 超时]
```

### 7.2 上下文关联标记
```
【前置条件】: 明确说明执行前需要满足的条件
【数据依赖】: 说明依赖的数据来源和格式
【接口依赖】: 说明依赖的接口调用顺序
【状态依赖】: 说明依赖的系统或数据状态

示例：
【前置条件】: 系统中存在有效的个人用户
【数据依赖】: 需要有效的用户ID和手机号
【接口依赖】: 必须先调用用户注册接口
【状态依赖】: 用户状态为"已激活"
```

## 8. 质量检查清单

### 8.1 用例完整性检查
- [ ] 用例标识符合命名规范
- [ ] 测试目标明确具体
- [ ] 执行步骤详细完整
- [ ] 参数描述准确清晰
- [ ] 预期结果可验证
- [ ] 数据关联关系明确

### 8.2 MCP集成检查
- [ ] 所需MCP方法已定义
- [ ] MCP方法参数完整
- [ ] 数据流转关系清晰
- [ ] 异常处理场景覆盖

### 8.3 大模型友好性检查
- [ ] 使用标准化关键词
- [ ] 避免歧义表达
- [ ] 上下文关系明确
- [ ] 步骤逻辑清晰

## 9. 实施建议

### 9.1 推广策略
1. **试点推行**: 选择核心模块先行试点
2. **培训推广**: 组织团队培训和实践
3. **工具支持**: 开发用例模板和检查工具
4. **持续优化**: 根据使用反馈持续改进

### 9.2 配套工具
1. **用例模板库**: 提供各类场景的标准模板
2. **MCP方法库**: 建立完整的MCP方法清单
3. **质量检查工具**: 自动化用例质量检查
4. **转换工具**: 用例到HttpRunner代码的转换工具

## 10. 示例用例

### 10.1 完整示例：证书申请流程测试
```
【用例标题】: 创建个人证书
【测试类型】: [功能测试]
【优先级】: P0
【依赖关系】: 无
【标签】: api, user, register

【执行步骤】:
步骤1: 准备测试数据
  - 调用创建测试账户接口，获取个人证件号、证件类型、姓名、手机号
  - 检查响应状态码为200

步骤2: 创建证书
  - 使用步骤1的数据，调用创建证书接口，获取证书id
  - 请求参数：手机号=${步骤1.手机号}，证件号=${步骤1.证件号}，证件类型=${步骤1.证件类型}，姓名=${步骤1.姓名}
  - 检查响应状态码为200

步骤3: 查询证书信息
  - 使用步骤2的证书id，调用查询证书详情接口
  - 检查返回的手机号、证件号、证件类型、姓名与输入一致

步骤4: 准备更新数据
  - 调用创建测试账户接口，获取个人证件号、证件类型、姓名、手机号
  - 检查响应状态码为200

步骤5: 更新证书
  - 使用步骤2的证书id、步骤4的数据，调用更新证书接口
  - 请求参数：id=${步骤3.id}，手机号=${步骤4.手机号}，证件号=${步骤4.证件号}，证件类型=${步骤4.证件类型}，姓名=${步骤4.姓名}
  - 检查响应状态码为200

步骤6: 查询证书信息
  - 使用步骤2的证书id，调用查询证书详情接口
  - 检查返回的手机号、证件号、证件类型、姓名与步骤4返回的一致

步骤7: 注销证书信息
  - 使用步骤2的证书id，调用注销证书接口
  - 检查响应状态码为200

步骤8: 查询证书信息
  - 使用步骤2的证书id，调用查询证书详情接口
  - 返回证书被注销

步骤9: 更新证书信息
  - 使用步骤2的证书id、步骤1的数据，调用更新证书接口
  - 请求参数：id=${步骤3.id}，手机号=${步骤1.手机号}，证件号=${步骤1.证件号}，证件类型=${步骤1.证件类型}，姓名=${步骤1.姓名}
  - 返回证书被注销
```

---

**说明**: 这个规范确保了测试用例的标准化、可理解性和可执行性，为大模型自动生成HttpRunner测试代码提供了坚实的基础。通过统一的格式和明确的数据关联关系，大模型能够准确理解测试意图并生成高质量的自动化测试代码。
