# 标准化自动化测试用例规范（适用于大模型+HttpRunner+MCP）

## 1. 规范目标

- 统一测试用例编写格式，便于大模型理解和自动生成HttpRunner用例。
- 明确用例与MCP（Mock/数据生成平台）接口的衔接要求。
- 保证用例可落地、可追溯、可自动化。

---

## 2. 用例编写要求

### 2.1 用例结构
每个用例必须包含以下字段：

| 字段         | 说明                                                         | 必填 | 示例 |
|--------------|--------------------------------------------------------------|------|------|
| 用例编号     | 全局唯一，便于追踪                                           | 是   | TC-001 |
| 用例名称     | 简明扼要，突出测试目标                                       | 是   | 创建个人证书并延期 |
| 前置条件     | 测试前需满足的环境/数据/状态，若无则写"无"                   | 是   | 已注册用户 |
| 测试步骤     | 详细、逐步描述每一步操作，**每一步都要可落地、可执行**         | 是   | 1. 调用创建个人证书接口... |
| 预期结果     | 每一步的期望输出或状态变化，需与步骤一一对应                 | 是   | 1. 返回证书ID... |
| 依赖数据     | 涉及MCP数据生成的，需明确数据结构、生成方式、参数              | 否   | 见下方MCP要求 |
| 备注         | 其他补充说明                                                 | 否   | - |

### 2.2 步骤描述规范
- **每一步都要具体、可执行**，避免模糊描述（如"创建证书"应细化为"调用XX接口，参数为..."）。
- **接口调用需写明接口名、方法、关键参数**，如有默认参数需注明。
- **步骤间依赖要明确**，如"步骤2依赖步骤1返回的证书ID"。
- **断言/检查点要具体**，如"返回字段status为success"。

#### 示例（不合格 vs 合格）
- 不合格：创建一个证书。
- 合格：
  1. 调用`/api/certificate/create`接口，方法POST，参数：type=personal，name=张三。
  2. 断言返回字段`id`不为空。

---

## 3. MCP（Mock/数据生成平台）对接要求

### 3.1 MCP方法覆盖
- MCP需覆盖所有用例涉及的数据生成、接口Mock、依赖数据清理等能力。
- 每个用例步骤所需的MCP方法必须**明确、可调用**，如"生成个人证书数据"、"Mock证书延期接口返回"。

### 3.2 MCP参数规范
- **参数要素齐全**，如有默认值需在用例或MCP文档中说明。
- **数据依赖要可追溯**，如"生成的证书ID需传递给后续步骤"。
- **支持链式调用**，如"先生成证书，再延期，再查询"。

### 3.3 MCP与用例的映射
- 用例每一步都要能找到对应的MCP方法或接口。
- 若MCP暂不支持，需在用例备注中标明，便于后续补充。

---

## 4. 用例合规性自检清单
- [ ] 用例步骤是否足够详细、可落地？
- [ ] 每一步是否有明确的接口/操作/参数？
- [ ] 预期结果是否具体、可验证？
- [ ] MCP是否能满足所有数据/Mock需求？
- [ ] 步骤间依赖是否清晰？
- [ ] 是否有冗余或模糊描述？

---

## 5. 标准用例模板（Markdown格式）

```markdown
### 用例编号：TC-001
### 用例名称：创建个人证书并延期一年后查询

**前置条件：**
- 已注册用户，用户ID已知

**测试步骤：**
1. 调用`/api/certificate/create`接口，POST，参数：type=personal，name=张三
2. 断言返回字段`id`不为空，保存为`cert_id`
3. 调用`/api/certificate/extend`接口，POST，参数：id=`cert_id`，years=1
4. 断言返回字段`status`为`success`
5. 调用`/api/certificate/query`接口，GET，参数：id=`cert_id`
6. 断言返回字段`expire_date`为当前日期+1年

**预期结果：**
1. 成功返回证书ID
2. 延期接口返回成功
3. 查询接口返回的`expire_date`正确

**依赖数据/MCP说明：**
- 需MCP支持生成注册用户数据
- 需MCP支持证书创建、延期、查询接口的Mock或数据生成

**备注：**
- 若MCP暂不支持延期接口Mock，需补充开发
```

---

## 6. 常见问题与建议
- 用例描述不清、参数不全，导致大模型无法自动生成用例。
- 步骤间未明确依赖，自动化串联失败。
- MCP能力未覆盖，需及时补充。
- 建议用例评审时按本规范自查。

---

## 7. 参考
- [HttpRunner官方文档](https://httprunner.com/)
- [MCP平台接口文档]
- [大模型用例生成最佳实践] 