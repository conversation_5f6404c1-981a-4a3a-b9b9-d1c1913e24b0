#!/usr/bin/env python3
"""
启动过滤后的MCP服务
"""
import uvicorn
from app.core.app import create_app

def main():
    """启动过滤后的MCP服务"""
    print("🚀 启动过滤后的MCP服务")
    print("=" * 50)
    print("📍 服务地址: http://localhost:8006")
    print("🔗 MCP地址: http://127.0.0.1:8006/mcp")
    print("📚 API文档: http://localhost:8006/docs")
    print("✨ 特性: 只显示19个纯业务工具")
    print("=" * 50)
    
    # 创建应用
    app = create_app()
    
    # 启动服务
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8006,
        log_level="info"
    )

if __name__ == "__main__":
    main()
