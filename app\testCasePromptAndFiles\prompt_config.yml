# 提示词配置文件
# 用于定义提示词类型和文件配对关系
# 
# 格式说明：
# - 每个提示词类型包含：
#   - prompt_file: 提示词文件名
#   - reference_file: 参考文件名
#   - description: 功能描述
#   - method_name: 对应的方法名（可选）

prompt_types:
  枚举值集测:
    prompt_file: "枚举值提示词.txt"
    reference_file: "标准化用例参考格式.yml"
    description: "自动提取枚举参数并生成测试用例"
    method_name: "获取枚举值集测提示词和参考文件"
    
  必填参数集测:
    prompt_file: "必填参数提示词.txt"
    reference_file: "标准化用例参考格式.yml"
    description: "生成必填参数缺失场景的测试用例"
    method_name: "获取必填参数集测提示词和参考文件"

  必填与枚举集测:
    prompt_file: "必填与枚举合并提示词.txt"
    reference_file: "标准化用例参考格式.yml"
    description: "生成必填与枚举的测试用例"
    method_name: "获取必填与枚举集测提示词和参考文件"
    
  接口生成:
    prompt_file: "接口生成提示词.txt"
    reference_file: "标准接口参考格式.yml"
    description: "根据swagger文档生成标准接口文件"
    method_name: "获取接口生成提示词和参考文件"

# 未来可以轻松添加新的提示词类型，例如：
# 
# 边界值集测:
#   prompt_file: "边界值提示词.txt"
#   reference_file: "标准化用例参考格式.yml"
#   description: "生成边界值测试用例"
#   
# 性能测试:
#   prompt_file: "性能测试提示词.txt"
#   reference_file: "性能测试参考格式.yml"
#   description: "生成性能测试用例"

# 配置说明：
# 1. 添加新类型时，只需在 prompt_types 下添加新的配置项
# 2. 确保对应的提示词文件和参考文件存在于同一目录下
# 3. 系统会自动识别并支持新的提示词类型
# 4. 无需修改代码，完全配置驱动
