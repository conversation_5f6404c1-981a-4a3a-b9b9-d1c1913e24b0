import requests
import json

# 发送 GET 请求获取新的 Swagger 文档
response = requests.get("http://footstone-open-api.testk8s.tsign.cn/v2/api-docs")

# 检查请求是否成功
if response.status_code == 200:
    # 解析 JSON 数据
    swagger_data = response.json()
    
    # 将 JSON 数据写入文件
    with open("swagger_docs_new.json", "w", encoding="utf-8") as f:
        json.dump(swagger_data, f, ensure_ascii=False, indent=4)
    print("新的 Swagger 文档已成功保存为 swagger_docs_new.json")
else:
    print(f"无法获取新的 Swagger 文档，HTTP 状态码: {response.status_code}")