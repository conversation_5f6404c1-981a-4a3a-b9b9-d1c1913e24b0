"""
AI相关API端点
"""
from fastapi import APIRouter, HTTPException
from fastapi.responses import HTMLResponse
from typing import Dict, Any, List
import logging
import inspect

from app.services.ai_service import AIService
from app.services.simple_mcp_service import SimpleMCPService
from app.schemas.ai_schemas import (
    ChatRequest,
    ChatResponse,
    SimpleChatRequest,
    SimpleChatResponse,
    AvailableToolsResponse
)
from app.business_classes import get_all_business_methods

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/chat",
            description="AI对话接口，支持MCP工具调用",
            response_model=ChatResponse)
async def ai_chat(request: ChatRequest) -> Dict[str, Any]:
    """
    AI对话接口，支持自动调用MCP工具
    
    :param request: 聊天请求
    :return: AI响应和工具调用结果
    """
    try:
        ai_service = AIService()
        
        if request.use_tools:
            # 使用工具的对话
            conversation_history = None
            if request.conversation_history:
                conversation_history = [
                    {"role": msg.role, "content": msg.content} 
                    for msg in request.conversation_history
                ]
            
            result = await ai_service.chat_with_tools(
                message=request.message,
                conversation_history=conversation_history
            )
            return result
        else:
            # 简单对话
            message = await ai_service.simple_chat(request.message)
            return {
                "message": message,
                "tool_calls": [],
                "tool_results": []
            }
            
    except Exception as e:
        logger.error(f"AI对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI对话失败: {str(e)}")

@router.post("/simple-chat",
            description="简单AI对话接口，不使用工具",
            response_model=SimpleChatResponse)
async def simple_ai_chat(request: SimpleChatRequest) -> Dict[str, Any]:
    """
    简单AI对话接口，不调用任何工具
    
    :param request: 简单聊天请求
    :return: AI回复
    """
    try:
        ai_service = AIService()
        message = await ai_service.simple_chat(request.message)
        return {"message": message}
        
    except Exception as e:
        logger.error(f"简单AI对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"简单AI对话失败: {str(e)}")

@router.get("/tools",
           description="获取可用的MCP工具列表（已过滤）",
           response_model=AvailableToolsResponse)
async def get_available_tools() -> Dict[str, Any]:
    """
    获取可用的MCP工具列表（已应用过滤）

    :return: 可用工具列表
    """
    try:
        from app.core.mcp_config import should_exclude_method

        ai_service = AIService()
        tools = ai_service.available_tools

        # 格式化工具信息并应用过滤
        formatted_tools = []
        for tool in tools:
            function_info = tool["function"]
            tool_name = function_info["name"]

            # 🚫 应用过滤逻辑
            if should_exclude_method(tool_name):
                logger.debug(f"🚫 过滤排除工具: {tool_name}")
                continue

            formatted_tools.append({
                "name": tool_name,
                "description": function_info["description"],
                "parameters": function_info["parameters"]
            })
            logger.debug(f"✅ 包含业务工具: {tool_name}")

        logger.info(f"🎯 过滤后的工具数量: {len(formatted_tools)}")
        return {
            "tools": formatted_tools,
            "count": len(formatted_tools)
        }

    except Exception as e:
        logger.error(f"获取工具列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取工具列表失败: {str(e)}")

@router.post("/test-tool/{tool_name}",
            description="测试特定MCP工具")
async def test_mcp_tool(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """
    测试特定的MCP工具
    
    :param tool_name: 工具名称
    :param arguments: 工具参数
    :return: 工具调用结果
    """
    try:
        ai_service = AIService()
        result = await ai_service._call_mcp_tool(tool_name, arguments)
        return {
            "tool_name": tool_name,
            "arguments": arguments,
            "result": result
        }
        
    except Exception as e:
        logger.error(f"测试工具失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试工具失败: {str(e)}")

@router.get("/health",
           description="AI服务健康检查")
async def ai_health_check() -> Dict[str, Any]:
    """
    AI服务健康检查

    :return: 健康状态
    """
    try:
        ai_service = AIService()
        # 简单测试AI服务是否可用
        test_response = await ai_service.simple_chat("你好")

        return {
            "status": "healthy",
            "ai_service": "available",
            "test_response": test_response,
            "tools_count": len(ai_service.available_tools)
        }

    except Exception as e:
        logger.error(f"AI服务健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

# ============= 新增：统一MCP调用接口 =============

@router.get("/mcp/tools",
           description="获取所有可用的业务MCP工具列表（自动发现，已过滤）")
async def get_mcp_tools() -> Dict[str, Any]:
    """
    🚀 自动发现并返回所有可用的业务MCP工具（已应用过滤）

    :return: 工具列表和描述
    """
    try:
        from app.core.mcp_config import should_exclude_method

        tools = []

        # 🔍 自动发现业务类中的所有方法
        business_methods = get_all_business_methods()

        for name, instance, method in business_methods:
            if not name.startswith('_'):
                # 🚫 应用过滤逻辑
                if should_exclude_method(name):
                    logger.debug(f"🚫 过滤排除方法: {name}")
                    continue

                # 获取方法签名
                sig = inspect.signature(method)

                # 构建工具描述
                tool_info = {
                    "name": name,
                    "description": _clean_docstring(method.__doc__) or f"调用{name}方法",
                    "class": instance.__class__.__name__,
                    "parameters": {}
                }

                # 解析参数
                for param_name, param in sig.parameters.items():
                    param_info = {
                        "type": "string",  # 默认类型
                        "required": param.default == inspect.Parameter.empty
                    }

                    # 根据类型注解设置参数类型
                    if param.annotation != inspect.Parameter.empty:
                        if param.annotation == int:
                            param_info["type"] = "integer"
                        elif param.annotation == float:
                            param_info["type"] = "number"
                        elif param.annotation == bool:
                            param_info["type"] = "boolean"
                        elif param.annotation == list or str(param.annotation).startswith('typing.List'):
                            param_info["type"] = "array"
                        elif param.annotation == dict or str(param.annotation).startswith('typing.Dict'):
                            param_info["type"] = "object"

                    # 设置默认值
                    if param.default != inspect.Parameter.empty:
                        param_info["default"] = param.default
                        param_info["required"] = False

                    tool_info["parameters"][param_name] = param_info

                tools.append(tool_info)
                logger.debug(f"✅ 包含业务工具: {name}")

        logger.info(f"🎯 过滤后的业务工具数量: {len(tools)}")
        return {
            "success": True,
            "tools": tools,
            "count": len(tools),
            "message": "自动发现的业务MCP工具列表（已过滤）",
            "source": "business layer"
        }

    except Exception as e:
        logger.error(f"获取业务MCP工具列表失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "tools": [],
            "count": 0
        }

def _clean_docstring(docstring: str) -> str:
    """
    清理文档字符串，提取简洁的描述

    :param docstring: 原始文档字符串
    :return: 清理后的描述
    """
    if not docstring:
        return ""

    # 提取第一行作为简短描述
    lines = docstring.strip().split('\n')
    for line in lines:
        line = line.strip()
        if line and not line.startswith(':'):
            return line

    return docstring.strip()

@router.post("/mcp/call",
            description="统一的业务MCP工具调用接口")
async def call_mcp_tool(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    🚀 统一的业务MCP工具调用接口 - 这是AI使用业务MCP的唯一入口

    请求格式:
    {
        "tool_name": "工具名称",
        "parameters": {
            "param1": "value1",
            "param2": "value2"
        }
    }

    :param request: 工具调用请求
    :return: 工具调用结果
    """
    try:
        tool_name = request.get("tool_name")
        parameters = request.get("parameters", {})

        if not tool_name:
            return {
                "success": False,
                "error": "缺少tool_name参数",
                "tool_name": None,
                "parameters": parameters
            }

        # 🔍 从业务类查找方法（应用过滤）
        from app.core.mcp_config import should_exclude_method

        business_methods = get_all_business_methods()
        # 过滤掉不应该暴露的方法
        filtered_methods = []
        for name, instance, method in business_methods:
            if not should_exclude_method(name):
                filtered_methods.append((name, instance, method))

        method_dict = {name: (instance, method) for name, instance, method in filtered_methods}

        if tool_name not in method_dict:
            # 检查是否是被过滤的方法
            all_method_dict = {name: (instance, method) for name, instance, method in business_methods}
            if tool_name in all_method_dict:
                return {
                    "success": False,
                    "error": f"业务方法 {tool_name} 不可用（已被过滤）",
                    "tool_name": tool_name,
                    "parameters": parameters,
                    "available_tools": list(method_dict.keys())
                }
            else:
                return {
                    "success": False,
                    "error": f"业务方法 {tool_name} 不存在",
                    "tool_name": tool_name,
                    "parameters": parameters,
                    "available_tools": list(method_dict.keys())
                }

        # 获取业务方法
        instance, method = method_dict[tool_name]

        # 调用业务方法
        logger.info(f"🔧 调用业务MCP方法: {tool_name}, 参数: {parameters}")
        result = method(**parameters)

        return {
            "success": True,
            "tool_name": tool_name,
            "parameters": parameters,
            "result": result,
            "message": f"成功调用业务工具 {tool_name}",
            "source": "business layer"
        }

    except Exception as e:
        logger.error(f"业务MCP工具调用失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "tool_name": request.get("tool_name"),
            "parameters": request.get("parameters", {})
        }

# ============= 新增：简化的MCP使用方式 =============

@router.post("/simple-mcp/chat",
            description="简化的MCP对话接口 - 通过HTTP使用MCP")
async def simple_mcp_chat(request: ChatRequest) -> Dict[str, Any]:
    """
    简化的MCP对话接口

    这个接口展示了如何通过HTTP地址简单地使用MCP功能：
    1. AI只需要知道一个HTTP地址
    2. 自动发现所有可用工具
    3. 统一的调用格式

    :param request: 聊天请求
    :return: AI响应和工具调用结果
    """
    try:
        # 使用简化的MCP服务
        simple_mcp = SimpleMCPService()

        conversation_history = None
        if request.conversation_history:
            conversation_history = [
                {"role": msg.role, "content": msg.content}
                for msg in request.conversation_history
            ]

        result = await simple_mcp.chat_with_mcp(
            message=request.message,
            conversation_history=conversation_history
        )

        return result

    except Exception as e:
        logger.error(f"简化MCP对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"简化MCP对话失败: {str(e)}")

@router.get("/simple-mcp/info",
           description="获取简化MCP服务信息")
async def get_simple_mcp_info() -> Dict[str, Any]:
    """
    获取简化MCP服务的配置信息

    :return: MCP服务配置信息
    """
    try:
        simple_mcp = SimpleMCPService()
        info = simple_mcp.get_mcp_info()

        # 添加额外的使用说明
        info["benefits"] = [
            "只需要配置一个HTTP地址",
            "自动发现所有可用工具",
            "统一的调用格式",
            "无需修改多个配置文件",
            "新增工具自动生效"
        ]

        info["cursor_config"] = {
            "description": "在Cursor中使用MCP的配置",
            "mcp_server_url": info["mcp_base_url"],
            "example_usage": {
                "get_tools": f"curl {info['tools_endpoint']}",
                "call_tool": f"curl -X POST {info['call_endpoint']} -H 'Content-Type: application/json' -d '{{\"tool_name\": \"创建有账号证书申请任务接口\", \"parameters\": {{\"个人or企业\": 1}}}}'"
            }
        }

        return info

    except Exception as e:
        logger.error(f"获取简化MCP信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取简化MCP信息失败: {str(e)}")

@router.get("/simple-mcp/demo",
           description="简化MCP使用演示")
async def simple_mcp_demo() -> Dict[str, Any]:
    """
    演示如何使用简化的MCP接口

    :return: 演示信息和示例
    """
    try:
        simple_mcp = SimpleMCPService()

        # 获取可用工具
        tools = await simple_mcp.get_available_tools()

        demo_info = {
            "title": "简化MCP使用演示",
            "description": "展示如何通过一个HTTP地址使用所有MCP功能",
            "available_tools": tools,
            "usage_examples": [
                {
                    "description": "获取工具列表",
                    "method": "GET",
                    "url": f"{simple_mcp.mcp_base_url}/tools",
                    "response_example": {
                        "success": True,
                        "tools": tools[:1] if tools else [],
                        "count": len(tools)
                    }
                },
                {
                    "description": "调用工具",
                    "method": "POST",
                    "url": f"{simple_mcp.mcp_base_url}/call",
                    "payload_example": {
                        "tool_name": "创建有账号证书申请任务接口",
                        "parameters": {
                            "个人or企业": 1,
                            "产品": [1],
                            "使用老数据": 1
                        }
                    },
                    "response_example": {
                        "success": True,
                        "tool_name": "创建有账号证书申请任务接口",
                        "parameters": {"个人or企业": 1},
                        "result": {"status": "success", "task_id": "example_task_id"}
                    }
                }
            ],
            "integration_guide": {
                "step1": "启动服务: python main.py",
                "step2": f"获取工具列表: GET {simple_mcp.mcp_base_url}/tools",
                "step3": f"调用工具: POST {simple_mcp.mcp_base_url}/call",
                "step4": "在AI中配置MCP地址即可使用所有功能"
            }
        }

        return demo_info

    except Exception as e:
        logger.error(f"简化MCP演示失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"简化MCP演示失败: {str(e)}")

# ============= 前端页面路由 =============

@router.get("/chat-ui",
           description="AI聊天界面",
           response_class=HTMLResponse)
async def chat_ui():
    """
    返回AI聊天界面HTML页面

    :return: HTML页面
    """
    try:
        with open("app/static/chat.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="聊天界面文件未找到")
    except Exception as e:
        logger.error(f"加载聊天界面失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"加载聊天界面失败: {str(e)}")

@router.get("/advanced-chat-ui",
           description="高级AI聊天界面",
           response_class=HTMLResponse)
async def advanced_chat_ui():
    """
    返回高级AI聊天界面HTML页面

    :return: HTML页面
    """
    try:
        with open("app/static/advanced_chat.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="高级聊天界面文件未找到")
    except Exception as e:
        logger.error(f"加载高级聊天界面失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"加载高级聊天界面失败: {str(e)}")

# ============= MCP健康管理接口 =============

@router.get("/mcp/health",
           description="MCP服务健康检查")
async def mcp_health_check() -> Dict[str, Any]:
    """
    检查MCP服务健康状态

    :return: 健康状态信息
    """
    try:
        from app.services.mcp_health_service import mcp_health_service

        health_result = await mcp_health_service.check_mcp_health()
        connection_status = mcp_health_service.get_connection_status()

        return {
            "timestamp": health_result.get("last_check"),
            "health": health_result,
            "connection": connection_status,
            "recommendations": _get_health_recommendations(health_result, connection_status)
        }

    except Exception as e:
        logger.error(f"MCP健康检查失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "recommendations": ["检查MCP服务是否正常运行", "查看服务日志获取详细信息"]
        }

@router.post("/mcp/refresh",
            description="强制刷新MCP连接")
async def force_refresh_mcp() -> Dict[str, Any]:
    """
    强制刷新MCP连接和缓存

    解决MCP服务重启后需要手动刷新的问题

    :return: 刷新结果
    """
    try:
        from app.services.mcp_health_service import mcp_health_service

        refresh_result = await mcp_health_service.force_refresh()

        return {
            "success": True,
            "message": "MCP连接已强制刷新",
            "refresh_result": refresh_result,
            "next_steps": [
                "连接已重新建立",
                "缓存已清除",
                "工具列表已更新",
                "可以正常使用MCP功能"
            ]
        }

    except Exception as e:
        logger.error(f"强制刷新MCP连接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"强制刷新失败: {str(e)}")

@router.get("/mcp/status",
           description="获取MCP连接状态详情")
async def get_mcp_status() -> Dict[str, Any]:
    """
    获取详细的MCP连接状态信息

    :return: 连接状态详情
    """
    try:
        from app.services.mcp_health_service import mcp_health_service

        connection_status = mcp_health_service.get_connection_status()

        # 获取工具信息
        tools_result = await mcp_health_service.get_tools_with_health_check()

        return {
            "connection": connection_status,
            "tools": {
                "available": tools_result.get("success", False),
                "count": len(tools_result.get("tools", [])),
                "source": tools_result.get("source"),
                "cache_info": {
                    "expiry": tools_result.get("cache_expiry"),
                    "valid": connection_status.get("cache_valid", False)
                }
            },
            "service_info": {
                "base_url": "http://localhost:8000/api/ai/mcp",
                "endpoints": {
                    "tools": "/api/ai/mcp/tools",
                    "call": "/api/ai/mcp/call",
                    "health": "/api/ai/mcp/health"
                }
            },
            "troubleshooting": _get_troubleshooting_guide(connection_status)
        }

    except Exception as e:
        logger.error(f"获取MCP状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

def _get_health_recommendations(health_result: Dict[str, Any], connection_status: Dict[str, Any]) -> List[str]:
    """
    根据健康状态生成建议

    :param health_result: 健康检查结果
    :param connection_status: 连接状态
    :return: 建议列表
    """
    recommendations = []

    if health_result.get("status") == "healthy":
        recommendations.append("✅ MCP服务运行正常")
        if connection_status.get("cache_valid"):
            recommendations.append("📦 缓存有效，性能最佳")
        else:
            recommendations.append("🔄 缓存已过期，将自动刷新")
    else:
        recommendations.extend([
            "❌ MCP服务异常，建议检查：",
            "1. 确认MCP服务是否正在运行",
            "2. 检查网络连接是否正常",
            "3. 查看服务日志获取详细错误信息",
            "4. 尝试重启MCP服务",
            "5. 使用 /api/ai/mcp/refresh 强制刷新连接"
        ])

    return recommendations

def _get_troubleshooting_guide(connection_status: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成故障排除指南

    :param connection_status: 连接状态
    :return: 故障排除指南
    """
    guide = {
        "common_issues": {
            "service_restart": {
                "problem": "MCP服务重启后连接失效",
                "solution": "调用 POST /api/ai/mcp/refresh 强制刷新连接",
                "prevention": "系统已启用自动健康检查，通常会自动恢复"
            },
            "network_timeout": {
                "problem": "网络超时或连接失败",
                "solution": "检查网络连接，确认MCP服务端口可访问",
                "prevention": "系统会自动重试，通常无需手动干预"
            },
            "cache_stale": {
                "problem": "缓存过期导致工具列表不准确",
                "solution": "系统会自动刷新缓存，也可手动刷新",
                "prevention": f"缓存每{connection_status.get('health_check_interval', 30)}秒自动检查"
            }
        },
        "manual_steps": [
            "1. 检查MCP服务状态: GET /api/ai/mcp/health",
            "2. 强制刷新连接: POST /api/ai/mcp/refresh",
            "3. 查看详细状态: GET /api/ai/mcp/status",
            "4. 测试工具调用: POST /api/ai/mcp/call"
        ],
        "auto_recovery": {
            "enabled": True,
            "check_interval": f"{connection_status.get('health_check_interval', 30)}秒",
            "retry_attempts": connection_status.get('retry_attempts', 3),
            "features": [
                "自动健康检查",
                "连接断开自动重连",
                "智能缓存管理",
                "故障自动恢复"
            ]
        }
    }

    return guide
