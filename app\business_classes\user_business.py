"""
用户业务类
=========

只需要定义这个类，系统自动生成MCP工具！
"""
from typing import Dict, Any
from app.business_classes import BusinessBase
from app.services.user_service import UserService


class UserBusiness(BusinessBase):
    """用户管理业务类"""
    
    class Meta:
        name = "用户管理"
        icon = "👤"
        description = "管理用户信息，包括生成、验证、批量处理等功能"
        category = "core"
        ai_prompt_hint = "当用户需要用户相关操作时，如生成用户、验证身份、获取用户信息等，请使用此类的方法"
    
    def __init__(self):
        super().__init__()
        self.user_service = UserService()
    
    def 获取用户信息(self, 环境: int = 1) -> Dict[str, Any]:
        """
        获取用户或企业信息
        
        :param 环境: 环境类型 1-测试环境 2-模拟环境
        :return: 用户信息
        """
        try:
            # 自动处理异步调用
            result = self._run_async_method(
                self.user_service.get_user_or_enterprise_info(环境)
            )
            return result
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取用户信息失败: {str(e)}"
            }
    
    def 生成模拟用户(self, 用户类型: str = "personal") -> Dict[str, Any]:
        """
        生成模拟用户数据
        
        :param 用户类型: 用户类型 personal/enterprise
        :return: 模拟用户数据
        """
        try:
            # 自动处理异步调用
            result = self._run_async_method(
                self.user_service.generate_mock_user(用户类型)
            )
            return result
        except Exception as e:
            self.logger.error(f"生成模拟用户失败: {str(e)}")
            return {
                "status": "error",
                "message": f"生成模拟用户失败: {str(e)}"
            }
    
