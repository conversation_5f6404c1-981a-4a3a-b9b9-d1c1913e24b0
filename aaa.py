aa = """{
    "docs":
    [
        {
            "fileId": "${file_id}"
        }
    ],
    "signFlowConfig":
    {
        "noticeConfig":
        {
            "noticeTypes": "1,2"
        },
        "signFlowTitle": "3.0主流程场景：3.0轩辕#rsa#手绘+企业+手动+部分审批#可信签"
    },
    "signers":
    [
        {
            
            "psnSignerInfo":
            {
                "psnId": "${psn_id}"
            },
            "signFields":
            [
                {
                    "customBizNum": "",
                    "fileId": "${file_id}",
                    "mustSign": true,

                }
            ],
            "signerType": 0
        },
     
    ]
} """
# print(aa)
#打印aa这个json并去掉格式，让他们都在一行
cleaned_aa = aa.replace('\n', '').replace('\t', '')
print(cleaned_aa)
#去掉空格
print(cleaned_aa.replace(' ', ''))