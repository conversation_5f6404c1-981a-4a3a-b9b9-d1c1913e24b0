"""
MCP业务服务配置
==============

专门配置对外暴露的业务MCP工具，排除测试和内部方法
"""
from fastapi import FastAPI
from fastapi_mcp import FastApiMCP
from typing import List, Dict, Any
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# 🚫 排除列表 - 不对外暴露的方法/路径
EXCLUDE_METHODS = [
    # 健康检查相关
    "health_check_health_check_get",
    "ai_health_check_api_ai_health_get",
    "mcp_health_check_api_ai_mcp_health_get",

    # AI服务相关接口
    "ai_chat_api_ai_chat_post",
    "simple_ai_chat_api_ai_simple_chat_post",
    "get_available_tools_api_ai_tools_get",
    "test_mcp_tool_api_ai_test_tool__tool_name__post",
    "get_mcp_tools_api_ai_mcp_tools_get",
    "call_mcp_tool_api_ai_mcp_call_post",
    "simple_mcp_chat_api_ai_simple_mcp_chat_post",
    "get_simple_mcp_info_api_ai_simple_mcp_info_get",
    "simple_mcp_demo_api_ai_simple_mcp_demo_post",
    "force_refresh_mcp_api_ai_mcp_refresh_post",
    "get_mcp_status_api_ai_mcp_status_get",
    "advanced_chat_ui_api_ai_advanced_chat_ui_get",

    # 用户API方法
    "get_user_or_enterprise_info",
    "generate_mock_user_api_users_mock_user_post",

    # 内部测试方法
    "test_",
    "demo_",
    "_internal",
    "_test",

    # 其他不需要暴露的方法
    "health",
    "ping",
    "status",
    "openapi",
    "docs",
    "redoc"
]

# 🚫 排除路径模式
EXCLUDE_PATH_PATTERNS = [
    "/api/ai/",
    "/api/users/",
    "/docs",
    "/redoc",
    "/openapi.json"
]

def should_exclude_method(method_name: str, method_path: str = "") -> bool:
    """
    判断方法是否应该被排除
    
    :param method_name: 方法名称
    :param method_path: 方法路径
    :return: 是否排除
    """
    # 检查方法名是否在排除列表中
    for exclude_pattern in EXCLUDE_METHODS:
        if exclude_pattern in method_name.lower():
            return True
    
    # 检查路径是否匹配排除模式
    for exclude_pattern in EXCLUDE_PATH_PATTERNS:
        if exclude_pattern in method_path:
            return True
    
    # 排除私有方法
    if method_name.startswith('_'):
        return True
        
    return False



def create_business_mcp_server(app: FastAPI) -> FastApiMCP:
    """
    创建只包含业务方法的MCP服务器

    :param app: FastAPI应用实例
    :return: 配置好的MCP服务器
    """
    logger.info("🚀 创建业务MCP服务器...")

    # 🎯 创建一个只包含业务路由的独立应用
    business_app = create_filtered_business_app(app)

    # 基于过滤后的业务应用创建MCP服务器
    mcp = FastApiMCP(
        business_app,
        name=f"{settings.MCP_NAME} - 业务服务",
        description=get_business_mcp_description(),
        describe_all_responses=True,
        describe_full_response_schema=True
    )

    logger.info("✅ 业务MCP服务器创建完成")
    return mcp

def create_filtered_business_app(original_app: FastAPI) -> FastAPI:
    """
    创建一个只包含业务路由的过滤应用

    :param original_app: 原始FastAPI应用（未使用，保留参数兼容性）
    :return: 只包含业务路由的应用
    """
    logger.info("🔧 创建过滤的业务应用...")

    # 创建新的FastAPI应用，只包含业务路由
    business_app = FastAPI(
        title=f"{settings.PROJECT_NAME} - 业务工具",
        description="只包含业务工具的MCP服务",
        version=settings.VERSION,
    )

    # 只包含业务路由
    from app.api.business_routes import business_router
    business_app.include_router(business_router, prefix="/api")

    logger.info("✅ 业务应用创建完成")
    return business_app

def get_business_mcp_description() -> str:
    """
    获取业务MCP服务器描述

    :return: MCP服务器描述
    """
    return """🚀 专业的业务工具集合

📁 业务模块：
  📜 证书管理: 提供完整的证书增删改查功能
  👤 用户管理: 用户信息生成和验证
  🎲 数据生成: 各种测试数据生成工具
  🛠️ 自定义工具: 实用工具集合

✨ 特性：
  🔥 纯业务方法 - 只包含核心业务功能
  🛡️ 自动过滤 - 排除测试和内部方法
  📝 智能提示 - 详细的方法描述
  🌐 统一接口 - 标准化返回格式"""

def apply_route_filters(mcp: FastApiMCP):
    """
    应用路由过滤，移除不需要的路由

    :param mcp: MCP服务器实例
    """
    try:
        logger.info("🔧 应用路由过滤...")

        # 获取MCP服务器的工具列表
        if hasattr(mcp, '_tools') and mcp._tools:
            original_count = len(mcp._tools)

            # 过滤工具列表
            filtered_tools = {}
            for tool_name, tool_info in mcp._tools.items():
                if not should_exclude_method(tool_name):
                    filtered_tools[tool_name] = tool_info
                else:
                    logger.debug(f"🚫 排除工具: {tool_name}")

            # 更新工具列表
            mcp._tools = filtered_tools
            filtered_count = len(filtered_tools)

            logger.info(f"✅ 路由过滤完成: {original_count} -> {filtered_count} (排除了 {original_count - filtered_count} 个工具)")
        else:
            logger.warning("⚠️ 未找到MCP工具列表，可能需要在mount后再次过滤")

    except Exception as e:
        logger.error(f"❌ 应用路由过滤失败: {str(e)}")

def filter_mcp_tools_after_mount(mcp: FastApiMCP):
    """
    在MCP服务器挂载后再次过滤工具

    :param mcp: MCP服务器实例
    """
    try:
        logger.info("🔧 挂载后过滤MCP工具...")

        # 尝试访问不同的工具存储属性
        tools_attrs = ['_tools', 'tools', '_tool_registry', 'tool_registry']

        for attr in tools_attrs:
            if hasattr(mcp, attr):
                tools = getattr(mcp, attr)
                if tools and isinstance(tools, dict):
                    original_count = len(tools)

                    # 过滤工具
                    filtered_tools = {}
                    for tool_name, tool_info in tools.items():
                        if not should_exclude_method(tool_name):
                            filtered_tools[tool_name] = tool_info
                        else:
                            logger.debug(f"🚫 排除工具: {tool_name}")

                    # 更新工具列表
                    setattr(mcp, attr, filtered_tools)
                    filtered_count = len(filtered_tools)

                    logger.info(f"✅ 挂载后过滤完成 ({attr}): {original_count} -> {filtered_count}")
                    return

        logger.warning("⚠️ 未找到可过滤的工具属性")

    except Exception as e:
        logger.error(f"❌ 挂载后过滤失败: {str(e)}")


