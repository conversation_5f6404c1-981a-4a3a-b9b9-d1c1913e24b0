#!/usr/bin/env python3
"""
检查 MCP 工具名称格式
"""
import requests
import json


def check_mcp_tools():
    """检查 MCP 工具名称格式"""
    print("🔍 检查 MCP 工具名称格式")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 检查两种 MCP 实现的工具列表
    print("\n📋 1. HTTP API 工具列表 (/api/ai/mcp/tools)")
    try:
        response = requests.get(f"{base_url}/api/ai/mcp/tools")
        if response.status_code == 200:
            tools = response.json()
            prompt_tools = [tool for tool in tools if "提示词" in tool.get("description", "")]
            print(f"找到 {len(prompt_tools)} 个提示词工具:")
            for tool in prompt_tools:
                print(f"  - 名称: {tool['name']}")
                print(f"    描述: {tool['description']}")
                print()
        else:
            print(f"❌ 获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
    
    # 检查 OpenAPI 规范
    print("\n📋 2. OpenAPI 规范中的路径")
    try:
        response = requests.get(f"{base_url}/openapi.json")
        if response.status_code == 200:
            openapi_spec = response.json()
            paths = openapi_spec.get("paths", {})

            prompt_paths = [path for path in paths.keys() if "提示词" in path]
            print(f"找到 {len(prompt_paths)} 个提示词相关路径:")
            for path in prompt_paths:
                method_info = paths[path]
                for method, details in method_info.items():
                    operation_id = details.get("operationId", "")
                    summary = details.get("summary", "")
                    print(f"  - 路径: {path}")
                    print(f"    方法: {method.upper()}")
                    print(f"    操作ID: {operation_id}")
                    print(f"    摘要: {summary}")
                    print()
        else:
            print(f"❌ 获取 OpenAPI 规范失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

    # 测试新的综合接口
    print("\n🚀 3. 测试新的综合接口")
    try:
        response = requests.post(f"{base_url}/api/business/获取全套集测提示词")
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                data = result.get("data", {})
                print("✅ 综合接口调用成功")
                print(f"包含场景: {list(data.keys())}")

                # 检查每个场景的提示词长度
                for scenario, content in data.items():
                    if scenario != "usage_guide" and isinstance(content, dict):
                        prompt_length = len(content.get("full_prompt", ""))
                        print(f"  - {scenario}: {prompt_length} 字符")
            else:
                print(f"❌ 综合接口失败: {result.get('message')}")
        else:
            print(f"❌ 综合接口调用失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📝 分析:")
    print("1. HTTP API 使用中文方法名")
    print("2. FastApiMCP 可能使用 operationId 或路径生成工具名")
    print("3. 需要确保工具名称一致性")
    print("=" * 60)


if __name__ == "__main__":
    check_mcp_tools()
