<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 智能助手 - 高级多轮对话</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 95%;
            max-width: 1200px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: grid;
            grid-template-columns: 300px 1fr;
            overflow: hidden;
        }

        .sidebar {
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .conversation-list {
            margin-bottom: 20px;
        }

        .conversation-item {
            padding: 10px;
            margin-bottom: 10px;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .conversation-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .conversation-item.active {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .conversation-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .conversation-preview {
            font-size: 12px;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .conversation-time {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
        }

        .new-chat-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
            transition: transform 0.2s ease;
        }

        .new-chat-btn:hover {
            transform: translateY(-2px);
        }

        .settings-section {
            border-top: 1px solid #e0e0e0;
            padding-top: 20px;
        }

        .setting-item {
            margin-bottom: 15px;
        }

        .setting-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .setting-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle-switch {
            position: relative;
            width: 40px;
            height: 20px;
            background: #ccc;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #667eea;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        .main-chat {
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 20px;
            font-weight: 600;
        }

        .chat-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            order: 2;
        }

        .message.ai .message-avatar {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .message-time {
            font-size: 12px;
            opacity: 0.6;
            margin-top: 5px;
        }

        .tool-calls {
            margin-top: 10px;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .tool-call {
            font-size: 12px;
            margin: 5px 0;
            color: #667eea;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 15px 20px;
            background: white;
            border-radius: 20px;
            border-bottom-left-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        @media (max-width: 768px) {
            .chat-container {
                grid-template-columns: 1fr;
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .sidebar {
                display: none;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🤖 AI 助手</h2>
                <div style="font-size: 12px; opacity: 0.9;">高级多轮对话</div>
            </div>
            
            <div class="sidebar-content">
                <button class="new-chat-btn" onclick="startNewConversation()">
                    ➕ 新建对话
                </button>
                
                <div class="conversation-list">
                    <h3 style="font-size: 14px; margin-bottom: 10px; color: #666;">对话历史</h3>
                    <div id="conversation-list">
                        <!-- 对话历史将在这里动态生成 -->
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3 style="font-size: 14px; margin-bottom: 15px; color: #666;">设置</h3>
                    
                    <div class="setting-item">
                        <div class="setting-label">启用工具调用</div>
                        <div class="setting-toggle">
                            <div class="toggle-switch active" id="tools-toggle" onclick="toggleTools()"></div>
                            <span style="font-size: 12px;">开启</span>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-label">自动保存对话</div>
                        <div class="setting-toggle">
                            <div class="toggle-switch active" id="autosave-toggle" onclick="toggleAutoSave()"></div>
                            <span style="font-size: 12px;">开启</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主聊天区域 -->
        <div class="main-chat">
            <div class="chat-header">
                <div class="chat-title" id="chat-title">新对话</div>
                <div class="chat-actions">
                    <button class="action-btn" onclick="exportConversation()">📤 导出</button>
                    <button class="action-btn" onclick="clearCurrentChat()">🗑️ 清空</button>
                </div>
            </div>
            
            <div class="chat-messages" id="chat-messages">
                <div class="message ai">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div>你好！我是AI智能助手，支持高级多轮对话功能。</div>
                        <div>🚀 新功能：</div>
                        <div>• 💾 自动保存对话历史</div>
                        <div>• 🔄 多对话管理</div>
                        <div>• 📤 对话导出功能</div>
                        <div>• ⚙️ 个性化设置</div>
                        <div class="message-time" id="welcome-time"></div>
                    </div>
                </div>
            </div>
            
            <div class="typing-indicator" id="typing-indicator">
                <div class="message-avatar">🤖</div>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
            
            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea 
                        id="chat-input" 
                        class="chat-input" 
                        placeholder="请输入您的问题..."
                        rows="1"
                    ></textarea>
                    <button id="send-button" class="send-button">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let conversations = JSON.parse(localStorage.getItem('ai_conversations') || '[]');
        let currentConversationId = null;
        let currentConversationHistory = [];
        let isTyping = false;
        let useTools = true;
        let autoSave = true;

        // DOM 元素
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');
        const chatTitle = document.getElementById('chat-title');
        const conversationList = document.getElementById('conversation-list');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置欢迎消息时间
            document.getElementById('welcome-time').textContent = formatTime(new Date());

            // 加载对话历史
            loadConversationList();

            // 如果有保存的对话，加载最近的一个
            if (conversations.length > 0) {
                loadConversation(conversations[0].id);
            } else {
                startNewConversation();
            }

            // 自动调整输入框高度
            chatInput.addEventListener('input', autoResizeTextarea);

            // 回车发送消息
            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 发送按钮点击事件
            sendButton.addEventListener('click', sendMessage);

            // 聚焦输入框
            chatInput.focus();
        });

        // 工具函数
        function formatTime(date) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function autoResizeTextarea() {
            chatInput.style.height = 'auto';
            chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
        }

        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTypingIndicator() {
            typingIndicator.style.display = 'flex';
            scrollToBottom();
        }

        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        // 对话管理
        function startNewConversation() {
            currentConversationId = generateId();
            currentConversationHistory = [];

            // 清空聊天界面
            chatMessages.innerHTML = `
                <div class="message ai">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div>你好！我是AI智能助手，支持高级多轮对话功能。</div>
                        <div>🚀 新功能：</div>
                        <div>• 💾 自动保存对话历史</div>
                        <div>• 🔄 多对话管理</div>
                        <div>• 📤 对话导出功能</div>
                        <div>• ⚙️ 个性化设置</div>
                        <div class="message-time">${formatTime(new Date())}</div>
                    </div>
                </div>
            `;

            // 更新标题
            chatTitle.textContent = '新对话';

            // 创建新对话记录
            const newConversation = {
                id: currentConversationId,
                title: '新对话',
                messages: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            conversations.unshift(newConversation);
            saveConversations();
            loadConversationList();

            chatInput.focus();
        }

        function loadConversation(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (!conversation) return;

            currentConversationId = conversationId;
            currentConversationHistory = conversation.messages.map(m => ({
                role: m.role,
                content: m.content
            }));

            // 清空聊天界面
            chatMessages.innerHTML = '';

            // 重新渲染消息
            conversation.messages.forEach(message => {
                addMessageToUI(message.role, message.content, message.toolCalls, new Date(message.timestamp));
            });

            // 更新标题
            chatTitle.textContent = conversation.title;

            // 更新对话列表选中状态
            updateConversationListSelection(conversationId);

            scrollToBottom();
        }

        function saveCurrentConversation() {
            if (!currentConversationId || !autoSave) return;

            const conversation = conversations.find(c => c.id === currentConversationId);
            if (conversation) {
                conversation.updatedAt = new Date().toISOString();
                saveConversations();
            }
        }

        function saveConversations() {
            localStorage.setItem('ai_conversations', JSON.stringify(conversations));
        }

        function loadConversationList() {
            conversationList.innerHTML = '';

            if (conversations.length === 0) {
                conversationList.innerHTML = '<div style="text-align: center; color: #999; font-size: 12px;">暂无对话历史</div>';
                return;
            }

            conversations.forEach(conversation => {
                const item = document.createElement('div');
                item.className = 'conversation-item';
                item.onclick = () => loadConversation(conversation.id);

                const lastMessage = conversation.messages[conversation.messages.length - 1];
                const preview = lastMessage ? lastMessage.content.substring(0, 30) + '...' : '新对话';

                item.innerHTML = `
                    <div class="conversation-title">${conversation.title}</div>
                    <div class="conversation-preview">${preview}</div>
                    <div class="conversation-time">${formatTime(new Date(conversation.updatedAt))}</div>
                `;

                conversationList.appendChild(item);
            });
        }

        function updateConversationListSelection(selectedId) {
            const items = conversationList.querySelectorAll('.conversation-item');
            items.forEach((item, index) => {
                if (conversations[index].id === selectedId) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        // 消息处理
        function addMessageToUI(role, content, toolCalls = null, timestamp = new Date()) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            const avatar = role === 'user' ? '👤' : '🤖';
            const time = formatTime(timestamp);

            let toolCallsHtml = '';
            if (toolCalls && toolCalls.length > 0) {
                toolCallsHtml = '<div class="tool-calls">';
                toolCallsHtml += '<div style="font-weight: bold; margin-bottom: 5px;">🔧 工具调用:</div>';
                toolCalls.forEach(call => {
                    toolCallsHtml += `<div class="tool-call">• ${call.function}(${JSON.stringify(call.arguments)})</div>`;
                });
                toolCallsHtml += '</div>';
            }

            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div>${content}</div>
                    ${toolCallsHtml}
                    <div class="message-time">${time}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addMessageToConversation(role, content, toolCalls = null) {
            const message = {
                role: role === 'user' ? 'user' : 'assistant',
                content: content,
                toolCalls: toolCalls,
                timestamp: new Date().toISOString()
            };

            // 添加到当前对话历史
            currentConversationHistory.push({
                role: message.role,
                content: message.content
            });

            // 保存到对话记录
            if (currentConversationId && autoSave) {
                const conversation = conversations.find(c => c.id === currentConversationId);
                if (conversation) {
                    conversation.messages.push(message);

                    // 更新对话标题（使用第一条用户消息）
                    if (conversation.title === '新对话' && role === 'user') {
                        conversation.title = content.substring(0, 20) + (content.length > 20 ? '...' : '');
                        chatTitle.textContent = conversation.title;
                    }

                    saveCurrentConversation();
                    loadConversationList();
                }
            }
        }

        // 发送消息
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message || isTyping) return;

            // 添加用户消息到UI和对话记录
            addMessageToUI('user', message);
            addMessageToConversation('user', message);

            // 清空输入框
            chatInput.value = '';
            autoResizeTextarea();

            // 设置发送状态
            isTyping = true;
            sendButton.disabled = true;
            showTypingIndicator();

            try {
                // 发送请求
                const response = await fetch('/api/ai/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_history: currentConversationHistory.slice(-10), // 保留最近10轮对话
                        use_tools: useTools
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 隐藏打字指示器
                hideTypingIndicator();

                // 添加AI回复到UI和对话记录
                const aiMessage = data.message || '抱歉，我没有收到有效的回复。';
                addMessageToUI('ai', aiMessage, data.tool_calls);
                addMessageToConversation('ai', aiMessage, data.tool_calls);

            } catch (error) {
                console.error('发送消息失败:', error);
                hideTypingIndicator();
                const errorMessage = `❌ 抱歉，发送消息时出现错误：${error.message}`;
                addMessageToUI('ai', errorMessage);
                addMessageToConversation('ai', errorMessage);
            } finally {
                // 重置状态
                isTyping = false;
                sendButton.disabled = false;
                chatInput.focus();
            }
        }

        // 设置功能
        function toggleTools() {
            useTools = !useTools;
            const toggle = document.getElementById('tools-toggle');
            const label = toggle.nextElementSibling;

            if (useTools) {
                toggle.classList.add('active');
                label.textContent = '开启';
            } else {
                toggle.classList.remove('active');
                label.textContent = '关闭';
            }
        }

        function toggleAutoSave() {
            autoSave = !autoSave;
            const toggle = document.getElementById('autosave-toggle');
            const label = toggle.nextElementSibling;

            if (autoSave) {
                toggle.classList.add('active');
                label.textContent = '开启';
            } else {
                toggle.classList.remove('active');
                label.textContent = '关闭';
            }
        }

        // 操作功能
        function clearCurrentChat() {
            if (confirm('确定要清空当前对话吗？')) {
                currentConversationHistory = [];

                // 清空UI
                chatMessages.innerHTML = `
                    <div class="message ai">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                            <div>对话已清空！我是AI智能助手，可以帮助您完成各种任务。</div>
                            <div class="message-time">${formatTime(new Date())}</div>
                        </div>
                    </div>
                `;

                // 清空对话记录
                if (currentConversationId) {
                    const conversation = conversations.find(c => c.id === currentConversationId);
                    if (conversation) {
                        conversation.messages = [];
                        conversation.title = '新对话';
                        chatTitle.textContent = '新对话';
                        saveCurrentConversation();
                        loadConversationList();
                    }
                }

                chatInput.focus();
            }
        }

        function exportConversation() {
            if (!currentConversationId) return;

            const conversation = conversations.find(c => c.id === currentConversationId);
            if (!conversation || conversation.messages.length === 0) {
                alert('当前对话为空，无法导出。');
                return;
            }

            // 生成导出内容
            let exportContent = `# ${conversation.title}\n\n`;
            exportContent += `创建时间: ${new Date(conversation.createdAt).toLocaleString('zh-CN')}\n`;
            exportContent += `更新时间: ${new Date(conversation.updatedAt).toLocaleString('zh-CN')}\n\n`;
            exportContent += `---\n\n`;

            conversation.messages.forEach((message, index) => {
                const role = message.role === 'user' ? '👤 用户' : '🤖 AI助手';
                const time = new Date(message.timestamp).toLocaleString('zh-CN');

                exportContent += `## ${role} (${time})\n\n`;
                exportContent += `${message.content}\n\n`;

                if (message.toolCalls && message.toolCalls.length > 0) {
                    exportContent += `**工具调用:**\n`;
                    message.toolCalls.forEach(call => {
                        exportContent += `- ${call.function}(${JSON.stringify(call.arguments)})\n`;
                    });
                    exportContent += `\n`;
                }

                exportContent += `---\n\n`;
            });

            // 下载文件
            const blob = new Blob([exportContent], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${conversation.title}_${new Date().toISOString().split('T')[0]}.md`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
